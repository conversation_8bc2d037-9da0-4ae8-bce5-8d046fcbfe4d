[1/5] C:\Windows\system32\cmd.exe /C "cd /D D:\BLE\Bitbucket\Feature_ble_fw_esp32\APP_ESP32\build\esp-idf\esptool_py && C:\Users\<USER>\.espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 partition --type app D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/partition_table/partition-table.bin D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/Vantage_nxESP32_Int_Rel_1_2_0_0.bin"
Vantage_nxESP32_Int_Rel_1_2_0_0.bin binary size 0xa0840 bytes. Smallest app partition is 0x100000 bytes. 0x5f7c0 bytes (37%) free.

[2/5] Performing build step for 'bootloader'
[1/1] C:\Windows\system32\cmd.exe /C "cd /D D:\BLE\Bitbucket\Feature_ble_fw_esp32\APP_ESP32\build\bootloader\esp-idf\esptool_py && C:\Users\<USER>\.espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/bootloader.bin"

Bootloader binary size 0x51e0 bytes. 0x2e20 bytes (36%) free.


[3/5] No install step for 'bootloader'
[4/5] Completed 'bootloader'
[4/5] C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\esp\v5.4.1\esp-idf\components\esptool_py && C:\Users\<USER>\.espressif\tools\cmake\3.30.2\bin\cmake.exe -D IDF_PATH=C:/Users/<USER>/esp/v5.4.1/esp-idf -D SERIAL_TOOL=C:/Users/<USER>/.espressif/python_env/idf5.4_py3.11_env/Scripts/python.exe;;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/esptool/esptool.py;--chip;esp32s3 -D SERIAL_TOOL_ARGS=--before=default_reset;--after=hard_reset;write_flash;@flash_args -D WORKING_DIRECTORY=D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build -P C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/run_serial_tool.cmake"
esptool.py --chip esp32s3 -p COM26 -b 460800 --before=default_reset --after=hard_reset write_flash --flash_mode dio --flash_freq 80m --flash_size detect 0x0 bootloader/bootloader.bin 0x10000 Vantage_nxESP32_Int_Rel_1_2_0_0.bin 0x8000 partition_table/partition-table.bin 0xd000 ota_data_initial.bin
esptool.py v4.8.1
Serial port COM26
Connecting....
Chip is ESP32-S3 (QFN56) (revision v0.2)
Features: WiFi, BLE, Embedded PSRAM 8MB (AP_3v3)
Crystal is 40MHz
MAC: a0:85:e3:f1:8c:c4
Uploading stub...
Running stub...
Stub running...
Changing baud rate to 460800
Changed.
Configuring flash size...
Auto-detected Flash size: 16MB
Flash will be erased from 0x00000000 to 0x00005fff...
Flash will be erased from 0x00010000 to 0x000b0fff...
Flash will be erased from 0x00008000 to 0x00008fff...
Flash will be erased from 0x0000d000 to 0x0000efff...
SHA digest in image updated
Compressed 20960 bytes to 13374...
Writing at 0x00000000... (100 %)
Wrote 20960 bytes (13374 compressed) at 0x00000000 in 0.8 seconds (effective 212.4 kbit/s)...
Hash of data verified.
Compressed 657472 bytes to 386850...
Writing at 0x00010000... (4 %)
Writing at 0x0001cdd2... (8 %)
Writing at 0x00027519... (12 %)
Writing at 0x00030ec8... (16 %)
Writing at 0x000371c8... (20 %)
Writing at 0x0003d7a8... (25 %)
Writing at 0x000437eb... (29 %)
Writing at 0x00049352... (33 %)
Writing at 0x0004f415... (37 %)
Writing at 0x00055572... (41 %)
Writing at 0x0005b385... (45 %)
Writing at 0x00060d36... (50 %)
Writing at 0x000666a5... (54 %)
Writing at 0x0006c7c8... (58 %)
Writing at 0x00072584... (62 %)
Writing at 0x00078647... (66 %)
Writing at 0x0007e62e... (70 %)
Writing at 0x00083e5f... (75 %)
Writing at 0x0008a4e6... (79 %)
Writing at 0x000948c7... (83 %)
Writing at 0x0009ae7a... (87 %)
Writing at 0x000a02bd... (91 %)
Writing at 0x000a64b2... (95 %)
Writing at 0x000aca8a... (100 %)
Wrote 657472 bytes (386850 compressed) at 0x00010000 in 9.8 seconds (effective 534.0 kbit/s)...
Hash of data verified.
Compressed 3072 bytes to 153...
Writing at 0x00008000... (100 %)
Wrote 3072 bytes (153 compressed) at 0x00008000 in 0.1 seconds (effective 328.6 kbit/s)...
Hash of data verified.
Compressed 8192 bytes to 31...
Writing at 0x0000d000... (100 %)
Wrote 8192 bytes (31 compressed) at 0x0000d000 in 0.0 seconds (effective 2033.8 kbit/s)...
Hash of data verified.

Leaving...
Hard resetting via RTS pin...
