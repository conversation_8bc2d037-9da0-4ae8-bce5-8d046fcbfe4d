# Load esp32s3 ROM ELF symbols
define target hookpost-remote
set confirm off
  # if $_streq((char *) 0x3ff194ad, "Mar  1 2021")
  if (*(int*) 0x3ff194ad) == 0x2072614d && (*(int*) 0x3ff194b1) == 0x32203120 && (*(int*) 0x3ff194b5) == 0x313230
    add-symbol-file C:/Users/<USER>/.espressif/tools/esp-rom-elfs/20241011/esp32s3_rev0_rom.elf
  else
    echo Warning: Unknown esp32s3 ROM revision.\n
  end
set confirm on
end


# Load bootloader symbols
set confirm off
    add-symbol-file D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/bootloader.elf
set confirm on

# Load application symbols
file D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/Vantage_nxESP32_Int_Rel_1_2_0_2.elf
