/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : ble_request_handler.c
* @version        : 1.0.7
* @brief          : Handle the configuration file
* @details        : Handle the configuration file
*****************************************************************************
* @version 1.0.1                                Date : 09/07/2025
* [+] Added support to write config file
*****************************************************************************
* @version 1.0.2                                Date : 18/07/2025
* [+] Added support to fw file transfer
* [-] Removed unused macros
* [~] Moved 256 KB buffer from .diram to external_ram
*****************************************************************************
* @version 1.0.3                                Date : 23/07/2025
* [+] Added LED control functionality for BLE activity indication
*****************************************************************************
* @version 1.0.4                                Date : 24/07/2025
* Added ESP32 Timer 
* Added BLE Attribute Handles
* Modified Send Start Command for FW File transfer Functionality
*****************************************************************************
* @version 1.0.5                                Date : 26/07/2025
* Added Version Request Handler
*****************************************************************************
* @version 1.0.6                                Date : 28/07/2025
* Removed Auth Handler
*****************************************************************************
* @version 1.0.7                                Date : 29/07/2025
* Added Transfer Complete flag
*******************************************************************************/

#include <string.h>
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"
#include "ble_request_handler.h"
#include "ble_response_handler.h"
#include "fw_file_handler.h"
#include "crc.h"
#include "utility.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "led_control.h"

/* VARIABLES */
__attribute__((section(".ext_ram.bss"))) uint8_t write_file_buffer[BUFFER_SIZE];
static uint32_t buffer_index = 0;
bool transfer_complete = false;
static bool start_byte_flag = true;
static uint32_t count = 0;
static int active_half = 0; // 0 = first half, 1 = second half
static size_t buffer_offset = 0; // Offset within current half
uint32_t total, used;
bool version_request_flag = false;

extern TaskHandle_t fw_file_write_handle;
extern uint16_t config_att_handle;
extern uint16_t fw_file_att_handle;
extern uint16_t req_resp_att_handle;
extern void notify_client(uint16_t conn_handle, uint16_t attr_handle, uint8_t err_code);

/* FUNCTION PROTOTYPES */
static void write_config_file_handle(const uint8_t *data, uint32_t len);
static void process_received_data(void);
extern uint32_t get_total_flash_size(uint32_t* total, uint32_t* used);
static error_code_t ble_fw_file_handler(uart_data_pack_t* packet);
static void reset_fw_upload_vars(void);

volatile bool ack_flag = false;
volatile bool nack_flag = false;

/**
 * @brief Handles incoming data to send to UART.
 *
 * This function processes the received UART data buffer, parses commands,
 * verifies packet formats, or forwards the data for further handling.
 * It is typically invoked from the UART event task or a UART ISR context.
 *
 * @param data   Pointer to the UART data buffer.
 *
 * @return       0 if data is handled successfully,
 *               non-zero error code if parsing or validation fails.
 */
int32_t ble_request_handler(const uint8_t *data)
{
	error_code_t status = ESP_OK;
	uart_data_pack_t packet;

	packet.start_byte = data[START_BYTE_INDEX];
	if (packet.start_byte != START_BYTE)
	{
		return START_BYTE_ERROR;
	}
	
	packet.packet_type = data[PACKET_TYPE_INDEX];
	packet.length = byte_array_to_u32_little_endian(&data[LENGTH_START_INDEX]);

		if (packet.length > PAYLOAD_LEN)
	{
		ESP_LOGI("PACKET", "length: 0x%lX", packet.length);
		return INVALID_LEN;
	}

	/* 6 is the 1(start byte)+1(packet_type)+4(length) */
	packet.crc = byte_array_to_u32_little_endian(&data[HEADER_SIZE + packet.length]);

	/* TODO: FOR FUTURE USE. FOLLOWING COPY IS DONE */
	if (packet.length != 0 && packet.length <= PAYLOAD_LEN)
	{
		memcpy(packet.payload, &data[HEADER_SIZE], packet.length);
	}

	status = validate_crc(data, (HEADER_SIZE + packet.length), packet.crc);
	if (true != status)
	{
		#ifdef DEBUG_CMD
			ESP_LOGI("PACKET", "start_byte: 0x%X", packet.start_byte);
			ESP_LOGI("PACKET", "packet_type: 0x%X", packet.packet_type);
			ESP_LOGI("PACKET", "length: 0x%lX", packet.length);
			ESP_LOGI("PACKET", "crc: 0x%lX", packet.crc);
			ESP_LOGI("CRC ERROR: ", "crc: 0x%lX", packet.crc);
			led_set_error(true, false);  // RED LED: General error blink
		#endif
		return CRC_CHECK_ERROR;
	}
	switch (packet.packet_type)
	{
	case BLE_READ_CONFIG_REQUEST:
#ifdef DEBUG_CMD
		ESP_LOGI("BLE_REQUEST_HANDLER: ", "Received: BLE_READ_CONFIG_REQUEST");
#endif
		status = write_data(data, (HEADER_SIZE + CRC_LENGTH + packet.length));
		break;
	case BLE_VERSION_REQUEST:
#ifdef DEBUG_CMD
		ESP_LOGI("BLE_REQUEST_HANDLER: ", "Received: BLE_VERSION_REQUEST");
#endif
        version_request_flag = true;
		status = write_data(data, (HEADER_SIZE + packet.length + CRC_LENGTH));
		break;
	case BLE_WRITE_CONFIG_REQUEST:
#ifdef DEBUG_CMD
        ESP_LOGI("BLE_REQUEST_HANDLER: ", "Received:%ld", packet.length);
#endif
        write_config_file_handle(&data[HEADER_SIZE], packet.length);
        break;
    case BLE_FW_UPLOAD_START:
        #ifdef DEBUG_CMD
		ESP_LOGI("BLE Attribute", "config_att_handle: %d", config_att_handle);
		ESP_LOGI("BLE Attribute", "fw_file_att_handle: %d", fw_file_att_handle);
		ESP_LOGI("BLE Attribute", "req_resp_att_handle: %d", req_resp_att_handle);
		#endif
        /* ERASE THE FLASH REGION */
        ESP_LOGI("BLE_FW_UPLOAD_START", "Erasing");
        reset_fw_upload_vars();
        buffer_index = 0;
        status = erase_flash();
        notify_client(get_connection_handle(), req_resp_att_handle, BLE_ACK);
        break;
    case BLE_FW_UPLOAD_DATA:
        /* WRITE THE FW DATA TO FLASH */
        status = ble_fw_file_handler(&packet);      
        break;
    case BLE_FW_UPLOAD_END:
        /* SEND NOTIFICATION TO MOBILE AND NEXUS */
        ESP_LOGI("BLE_FW_UPLOAD_END", "Notify");
        get_total_flash_size(&total, &used);
        ESP_LOGI("FLASH_USED: ", "Total: %ld, Used: %ld", total, used);
        buffer_index = 0;
        count = 0;
        status = start_fw_bundle_transfer();
        if (status == ESP_OK)
        {
            notify_client(get_connection_handle(), req_resp_att_handle, BLE_ACK);
        }
        else{
            notify_client(get_connection_handle(), req_resp_att_handle, status);
        }        
        break;
    default:
        ESP_LOGI("PACKET ERROR", "Received: INVALID REQUEST %ld", (int32_t)packet.packet_type);
        status = INVALID_PACKET_ERROR;
        break;
    }
    if (status > 0){
        status = BLE_ACK;
    }
    return status;
}

/**
 * @brief Handles writing the configuration file data into storage.
 *
 * This function processes the incoming configuration file data 
 * and writes it into the internal configuration file buffer. 
 * It may perform boundary checks to prevent buffer overflows 
 * and prepare the data for later use or transmission.
 *
 * @param data   Pointer to the received configuration file data.
 * @param len    Length of the configuration file data in bytes.
 *
 * @return None.
 */
static void write_config_file_handle(const uint8_t *data, uint32_t len) {
    if (start_byte_flag == true) {
        start_byte_flag = false;
        write_file_buffer[0] = START_BYTE;
        write_file_buffer[1] = BLE_WRITE_CONFIG_REQUEST;
        buffer_index += HEADER_SIZE;
    }

    memcpy(&write_file_buffer[buffer_index], data, len);
    buffer_index += len;

    if (len < CHUNK_SIZE) {
        transfer_complete = true;
    }
    
    process_received_data();
}

/**
 * @brief Processes the data received and stored in the internal buffer.
 *
 * This function performs parsing, validation (such as CRC checks), and 
 * handles the received data appropriately. Depending on the data type, 
 * it may update system configurations, trigger further actions, or 
 * prepare a response.
 *
 * @note This function assumes that the data is already stored in a 
 *       global or static buffer and its length is correctly maintained.
 *
 * @return None.
 */
static void process_received_data(void) {
    if (!transfer_complete)
        return;

    uint32_t calc_crc = 0;
    u32_to_byte_array_little_endian(&write_file_buffer[LENGTH_START_INDEX], buffer_index - HEADER_SIZE);
    calc_crc = crc32(&write_file_buffer[0], buffer_index);
    ESP_LOGI("CRC: ", "%ld", calc_crc);
    u32_to_byte_array_little_endian(&write_file_buffer[buffer_index], calc_crc);
    write_data((const char *)write_file_buffer, buffer_index + CRC_LENGTH);
	ESP_LOGI("WRITE DATA: ", "LENGTH : %ld", buffer_index + CRC_LENGTH);

    // Reset buffer for next transfer
    buffer_index = 0;
    transfer_complete = false;
    start_byte_flag = true;
}

static void reset_fw_upload_vars(void){
    buffer_index = 0;
    transfer_complete = false;
    start_byte_flag = true;
    count = 0;
    active_half = 0; // 0 = first half, 1 = second half
    buffer_offset = 0; // Offset within current half
}

static error_code_t ble_fw_file_handler(uart_data_pack_t* packet) {
    error_code_t rc = ESP_OK;
    size_t base_offset = active_half * HALF_SIZE;
    bool last_packet = (packet->length < CHUNK_SIZE); //131072
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
	
    if (buffer_offset + packet->length > HALF_SIZE) {
        size_t remaining_space = HALF_SIZE - buffer_offset;
        // TO CALCULATE THE REMAINING SPACE THAT FILL THE BUFFER
        if (remaining_space > 0) {
            memcpy(&write_file_buffer[base_offset + buffer_offset], packet->payload, remaining_space);
        }
        vTaskNotifyGiveIndexedFromISR(fw_file_write_handle, active_half, &xHigherPriorityTaskWoken);
        
        // SWITCH TO OTHER HALF
        active_half = 1 - active_half;
        buffer_offset = 0;
        // WRITE THE LEFTOVER PART TO THE NEW HALF
        size_t leftover_len = packet->length - remaining_space;
        memcpy(&write_file_buffer[active_half * HALF_SIZE + buffer_offset], packet->payload + remaining_space, leftover_len);
        buffer_offset += leftover_len;
        if (xHigherPriorityTaskWoken) {
            portYIELD_FROM_ISR();
        }
    } 
    else {
        memcpy(&write_file_buffer[base_offset + buffer_offset], packet->payload, packet->length);
        buffer_offset += packet->length;        
        if (buffer_offset >= HALF_SIZE) {
            vTaskNotifyGiveIndexedFromISR(fw_file_write_handle, active_half, &xHigherPriorityTaskWoken);
            active_half = 1 - active_half;
            buffer_offset = 0;
        }
        if (xHigherPriorityTaskWoken) {
            portYIELD_FROM_ISR();
        }
    }

    if (last_packet) {
        ESP_LOGI("LAST PACKET", "Active Half: %d, Offset: %d pack Len: %ld", active_half, buffer_offset, packet->length);
        vTaskNotifyGiveIndexedFromISR(fw_file_write_handle, 2, &xHigherPriorityTaskWoken);
        set_last_packet(active_half, buffer_offset);
    }

    return rc;
}
 
