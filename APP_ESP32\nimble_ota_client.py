#!/usr/bin/env python3
"""
NimBLE OTA Client for ESP32
Sends firmware updates via BLE to ESP32 running NimBLE OTA server
Supports chunked transfer, ACK/NACK, resume capability, and CRC verification
"""

import asyncio
import argparse
import struct
import zlib
import os
import sys
import time
from typing import Optional, <PERSON><PERSON>
from bleak import BleakClient, BleakScanner
from bleak.backends.characteristic import BleakGATTCharacteristic

# BLE Service and Characteristic UUIDs (matching ESP32 implementation)
# These UUIDs must match exactly with the ESP32 actual service discovery results
# Based on actual service discovery from ESP32:
OTA_SERVICE_UUID = "def09abc-5678-1234-def0-9abc56781234"      # Service UUID
OTA_DATA_CHAR_UUID = "def09abc-5678-1234-def1-9abc56781234"    # Data characteristic
OTA_CONTROL_CHAR_UUID = "def09abc-5678-1234-def2-9abc56781234" # Control characteristic
OTA_STATUS_CHAR_UUID = "def09abc-5678-1234-def3-9abc56781234"  # Status characteristic
OTA_INFO_CHAR_UUID = "def09abc-5678-1234-def4-9abc56781234"    # Info characteristic

# OTA Commands (must match ESP32 ble_ota_service.h)
OTA_CMD_START = 0x01
OTA_CMD_STOP = 0x02
OTA_CMD_END = 0x03
OTA_CMD_ABORT = 0x04

# OTA Status Codes (must match ESP32 ota_status_t enum exactly)
OTA_STATUS_IDLE = 0x00
OTA_STATUS_READY = 0x01
OTA_STATUS_RECEIVING = 0x02
OTA_STATUS_VERIFYING = 0x03
OTA_STATUS_SUCCESS = 0x04
OTA_STATUS_ERROR = 0x05
OTA_STATUS_ACK = 0x06        # This is the success response for chunks
OTA_STATUS_NACK = 0x07

# ACK Response Codes (aliases for clarity)
OTA_ACK_SUCCESS = OTA_STATUS_ACK
OTA_ACK_ERROR = OTA_STATUS_ERROR

# OTA Error Codes (must match ESP32)
OTA_ERROR_NONE = 0x00
OTA_ERROR_INVALID_SIZE = 0x01
OTA_ERROR_WRITE_FAILED = 0x02
OTA_ERROR_VERIFY_FAILED = 0x03
OTA_ERROR_PARTITION_FAILED = 0x04
OTA_ERROR_SEQUENCE_ERROR = 0x05

# Transfer parameters
MAX_CHUNK_SIZE = 512  # Increased chunk size for faster transfer (matches ESP32 server)
HEADER_SIZE = 8
MAX_RETRIES = 3
ACK_TIMEOUT = 15.0  # Increased timeout for better reliability

class NimBLEOTAClient:
    def __init__(self, device_address: Optional[str] = None, verbose: bool = False):
        self.device_address = device_address
        self.verbose = verbose
        self.client: Optional[BleakClient] = None
        self.ack_received = asyncio.Event()
        self.last_ack_code = None
        self.last_ack_sequence = None
        self.last_error_code = None
        self.waiting_for_ack = False
        self.ota_completed = False
        self.ota_transfer_complete = False  # Flag to prevent reconnection after OTA success
        self.same_version_detected = False  # Flag for same version detection
        self.start_time = None
        self.last_progress_percent = -1
        
    def log(self, message: str, level: str = "INFO", force: bool = False):
        # Only show important messages unless verbose mode is enabled
        if force or level in ["ERROR", "WARNING"]:
            print(f"[{level}] {message}")
        elif self.verbose and level == "INFO":
            print(f"[{level}] {message}")

    def notification_handler(self, sender, data):
        """Handle status notifications from ESP32"""
        if len(data) >= 10:  # ota_status_response_t size: 1+1+4+4 = 10 bytes
            status, error, sequence, bytes_received = struct.unpack('<BBII', data[:10])
            # Only log notifications in verbose mode or for important status
            if self.verbose or status != OTA_STATUS_ACK:
                self.log(f"Received notification: status={status}, error={error}, sequence={sequence}, bytes={bytes_received}")

            self.last_ack_code = status
            self.last_ack_sequence = sequence
            self.last_error_code = error

            # Check for specific error conditions
            if status == OTA_STATUS_ERROR and error == OTA_ERROR_WRITE_FAILED and sequence == 1 and not self.same_version_detected:
                # This typically indicates same version rejection - show message only once
                print("\n" + "="*80)
                print("                    FIRMWARE VERSION CONFLICT DETECTED")
                print("="*80)
                print("❌ The device already has the same firmware version installed.")
                print("❌ ESP32 bootloader prevents downgrade/same-version updates for safety.")
                print("")
                print("💡 To proceed with the update:")
                print("   1. Update the version in CMakeLists.txt (e.g., 1.1.0.3 → 1.1.0.4)")
                print("   2. Rebuild the firmware: idf.py build")
                print("   3. Try flashing again")
                print("")
                print("   Current firmware appears to be the same version as what you're trying to flash.")
                print("="*80)
                self.same_version_detected = True

            # Check for OTA completion (only set flag if this is from END command)
            if status == OTA_STATUS_SUCCESS and sequence == 0:
                print("🎉 OTA verification completed! ESP32 will restart.")
                self.ota_completed = True

            self.ack_received.set()
    
    async def scan_for_devices(self, timeout: float = 10.0) -> list:
        """Scan for ESP32 OTA devices"""
        self.log("Scanning for ESP32 OTA devices...", force=True)

        devices = await BleakScanner.discover(timeout=timeout)
        ota_devices = []

        self.log(f"Found {len(devices)} BLE devices during scan:", force=True)
        for device in devices:
            device_name = device.name or "Unknown"
            self.log(f"  Device: {device_name} ({device.address})")

            # Check for your device naming patterns: MR, CT, COP, or ESP32
            if device.name and (device.name.startswith("MR") or
                               device.name.startswith("CT") or
                               device.name.startswith("COP") or
                               "ESP32" in device.name or
                               "ESP32_OTA" in device.name or
                               device.name == "ESP32_OTA"):
                self.log(f"Found target device: {device.name} ({device.address})", force=True)
                ota_devices.append(device)
            elif device.address.lower() == "a0:85:e3:f1:8c:c6":  # Your specific device
                self.log(f"Found target device by MAC: {device.name or 'Unknown'} ({device.address})", force=True)
                ota_devices.append(device)

        if not ota_devices:
            self.log("No matching ESP32 devices found", "WARNING", force=True)

        return ota_devices
    
    async def connect(self) -> bool:
        """Connect to ESP32 OTA device"""
        if not self.device_address:
            devices = await self.scan_for_devices()
            if not devices:
                self.log("No ESP32 devices found", "ERROR")
                return False

            # Use first found device
            self.device_address = devices[0].address
            print(f"Using device: {devices[0].name} ({self.device_address})")

        # Try connecting with retries
        max_retries = 3
        for attempt in range(max_retries):
            try:
                self.log(f"Connection attempt {attempt + 1}/{max_retries} to {self.device_address}")
                self.client = BleakClient(self.device_address)
                await self.client.connect(timeout=20.0)  # Increased timeout
                print(f"Connected to {self.device_address}")
                break
            except Exception as e:
                self.log(f"Connection attempt {attempt + 1} failed: {e}", "WARNING")
                if self.client:
                    try:
                        await self.client.disconnect()
                    except:
                        pass
                if attempt < max_retries - 1:
                    self.log("Waiting 2 seconds before retry...")
                    await asyncio.sleep(2)
                else:
                    self.log("All connection attempts failed", "ERROR")
                    return False

        try:

            # Request larger MTU for better throughput
            try:
                mtu = await self.client.get_mtu()
                self.log(f"Current MTU: {mtu} bytes")
                if mtu < 512:
                    self.log("Note: MTU < 512, large chunks may be fragmented")
            except Exception as e:
                self.log(f"MTU info unavailable: {e}")

            # Request optimized connection parameters (BLE Standard compliant)
            try:
                await self._request_fast_connection_params()
            except Exception as e:
                self.log(f"Connection parameter optimization failed: {e}")

            # Request 2M PHY for higher speed (if supported)
            try:
                await self._request_2m_phy()
            except Exception as e:
                self.log(f"2M PHY request failed (using 1M): {e}")

            # Allow time for parameter negotiation
            await asyncio.sleep(0.2)

            # Skip pairing for now - ESP32 doesn't have bonding/pairing configured
            self.log("Skipping pairing - using unencrypted connection")

            # Discover services to verify OTA service is available
            try:
                services = self.client.services
                ota_service_found = False
                self.log(f"Looking for OTA service: {OTA_SERVICE_UUID}")

                for service in services:
                    service_uuid_str = str(service.uuid).lower()
                    expected_uuid_str = OTA_SERVICE_UUID.lower()
                    self.log(f"Found service: {service.uuid}")
                    self.log(f"  Comparing: '{service_uuid_str}' == '{expected_uuid_str}'")

                    if service_uuid_str == expected_uuid_str:
                        ota_service_found = True
                        print("OTA service found!")
                        for char in service.characteristics:
                            self.log(f"  Characteristic: {char.uuid} (properties: {char.properties})")

                if not ota_service_found:
                    self.log("OTA service not found on device", "ERROR")
                    self.log(f"Expected: {OTA_SERVICE_UUID}", "ERROR")
                    self.log("Available services:", "ERROR")
                    for service in services:
                        self.log(f"  - {service.uuid}", "ERROR")
                    await self.client.disconnect()
                    return False
            except Exception as e:
                self.log(f"Service discovery failed: {e}", "WARNING")
                self.log("Continuing without service verification...")

            # Enable notifications for status characteristic
            try:
                await self.client.start_notify(OTA_STATUS_CHAR_UUID, self.notification_handler)
                self.log("Notifications enabled for status characteristic")
            except Exception as e:
                self.log(f"Failed to enable notifications: {e}", "WARNING")
                self.log("Falling back to polling mode")

            self.log("Connection successful. Notifications enabled for ACKs.")
            return True

        except Exception as e:
            self.log(f"Service discovery or notification setup failed: {e}", "ERROR")
            try:
                await self.client.disconnect()
            except:
                pass
            return False
    
    async def _request_2m_phy(self):
        """Request 2M PHY for higher speed"""
        try:
            # This is platform-specific and may not work on all systems
            # Windows/Android: Use platform-specific APIs
            # Linux: May require direct HCI commands

            # For now, we'll try a generic approach that works on some platforms
            if hasattr(self.client, '_backend'):
                backend = self.client._backend

                # Try Windows WinRT backend
                if hasattr(backend, '_requester'):
                    self.log("Requesting 2M PHY (Windows)...")
                    # Windows doesn't have direct PHY control, but we can optimize connection params
                    pass

                # Try Android backend
                elif hasattr(backend, '_java_gatt'):
                    self.log("Requesting 2M PHY (Android)...")
                    # Android has PHY control through BluetoothGatt.setPreferredPhy()
                    pass

                # Try Linux BlueZ backend
                elif hasattr(backend, '_device_path'):
                    self.log("Requesting 2M PHY (Linux)...")
                    # Linux would need direct HCI commands
                    pass

                else:
                    self.log("2M PHY: Platform not supported, using 1M PHY")

            # Alternative: Request optimized connection parameters
            await self._request_fast_connection_params()

        except Exception as e:
            self.log(f"2M PHY request failed: {e}")

    async def _request_fast_connection_params(self):
        """Request faster connection parameters for better throughput (BLE Standard)"""
        try:
            self.log("Requesting optimized connection parameters (7.5-15ms interval)...")

            # The ESP32 peripheral will initiate the connection parameter update
            # This is the standard BLE approach - peripheral requests, central accepts
            # We just need to be ready to accept the ESP32's parameter update request

            # Most BLE client libraries don't expose direct connection parameter control
            # because it's typically handled by the peripheral (ESP32 in our case)
            # The ESP32 will request optimal parameters and the OS will usually accept them

            self.log("Connection parameter optimization requested (handled by ESP32)")

        except Exception as e:
            self.log(f"Connection parameter optimization failed: {e}")

    async def reconnect(self) -> bool:
        """Attempt to reconnect after connection loss"""
        try:
            # Add debug logging to track reconnection attempts
            print("🔄 RECONNECTION ATTEMPT DETECTED!")
            print(f"🔄 OTA completed flag: {self.ota_completed}")
            print(f"🔄 OTA transfer complete flag: {self.ota_transfer_complete}")

            self.log("Attempting to reconnect...")

            # Disconnect if partially connected
            if self.client:
                try:
                    await self.client.disconnect()
                except:
                    pass

            # Wait a moment for ESP32 to start advertising
            await asyncio.sleep(2.0)

            # Reconnect
            if await self.connect():
                self.log("Reconnection successful!")
                return True
            else:
                self.log(" Reconnection failed")
                return False

        except Exception as e:
            self.log(f"Reconnection error: {e}", "ERROR")
            return False

    async def disconnect(self):
        """Disconnect from device"""
        if self.client and self.client.is_connected:
            await self.client.disconnect()
            self.log("Disconnected")
    
    async def wait_for_ack(self, expected_sequence: int, timeout: float = ACK_TIMEOUT) -> Tuple[int, int]:
        """Wait for ACK notification from ESP32."""
        # Only log in verbose mode to reduce spam
        if self.verbose:
            self.log(f"Waiting for ACK notification on sequence {expected_sequence}...")

        try:
            # Wait for notification with timeout
            await asyncio.wait_for(self.ack_received.wait(), timeout=timeout)

            # Check if we got the expected sequence
            if self.last_ack_sequence == expected_sequence:
                if self.verbose:
                    self.log(f"ACK confirmed for sequence {expected_sequence} via notification.")
                return self.last_ack_code, self.last_ack_sequence
            else:
                self.log(f"Sequence mismatch: expected {expected_sequence}, got {self.last_ack_sequence}", "WARNING")
                return OTA_ACK_ERROR, self.last_ack_sequence

        except asyncio.TimeoutError:
            self.log(f"ACK timeout for sequence {expected_sequence}", "WARNING")
            return OTA_ACK_ERROR, expected_sequence
    
    async def send_control_command(self, command: int, data: bytes = b'') -> bool:
        """Send control command"""
        cmd_data = struct.pack('<B', command) + data

        try:
            await self.client.write_gatt_char(OTA_CONTROL_CHAR_UUID, cmd_data)
            self.log(f"Sent control command: 0x{command:02X}")
            # Small delay to ensure ESP32 processes command before we wait for ACK
            await asyncio.sleep(0.1)
            return True
        except Exception as e:
            self.log(f"Failed to send control command: {e}", "ERROR")
            return False
    
    async def read_status(self) -> Optional[dict]:
        """Read current OTA status from ESP32"""
        try:
            # Try to read the status characteristic
            status_data = await self.client.read_gatt_char(OTA_STATUS_CHAR_UUID)

            if len(status_data) >= 10:  # Expected size: 1+1+4+4 = 10 bytes
                # Parse the status response according to ota_status_response_t structure
                # typedef struct {
                #     uint8_t status;          // 1 byte
                #     uint8_t error;           // 1 byte
                #     uint32_t sequence_number; // 4 bytes
                #     uint32_t bytes_received; // 4 bytes
                # } __attribute__((packed)) ota_status_response_t;

                status = status_data[0]
                error = status_data[1]
                sequence_number = int.from_bytes(status_data[2:6], 'little')
                bytes_received = int.from_bytes(status_data[6:10], 'little')

                self.log(f"Status read successful: status={status}, error={error}, seq={sequence_number}, bytes={bytes_received}")
                return {
                    'state': status,
                    'progress': 0,  # Progress calculated elsewhere
                    'received_bytes': bytes_received,
                    'total_size': 0,  # Not in status response
                    'last_sequence': sequence_number,
                    'target_slot': 0,
                    'resume_mode': False,
                    'error_code': error
                }
            else:
                self.log(f"Unexpected status data length: {len(status_data)}", "WARNING")
                return None

        except Exception as e:
            self.log(f"Status read not supported, using polling mode", "WARNING")
            # Fallback to default status
            return {
                'state': 0,  # Assume ready state
                'progress': 0,
                'received_bytes': 0,
                'total_size': 0,
                'last_sequence': 0,
                'target_slot': 0,
                'resume_mode': False
            }

        return None
    
    async def start_ota(self, slot: int, firmware_size: int, firmware_crc: int) -> bool:
        """Start OTA transfer using existing BLE OTA service"""
        try:
            # Step 1: Send firmware info to Info characteristic first
            # ota_firmware_info_t structure: total_size + chunk_size + crc32 + version[16]
            chunk_size = 500  # Default chunk size
            # Generate version string with timestamp
            import datetime
            timestamp = datetime.datetime.now().strftime("%y%m%d_%H%M")
            version_str = f"v1.1.0_{timestamp}"
            version = version_str.encode('utf-8')[:16].ljust(16, b'\x00')  # Pad to 16 bytes
            firmware_info = struct.pack('<III16s', firmware_size, chunk_size, firmware_crc, version)

            # Debug: Check connection before Info write
            print(f"DEBUG: Connection status before Info write: {self.client.is_connected}")
            print(f"DEBUG: About to write {len(firmware_info)} bytes to Info characteristic")

            await self.client.write_gatt_char(OTA_INFO_CHAR_UUID, firmware_info)
            print(f"✅ Successfully wrote firmware info")
            print(f"Firmware version: {version_str}")
            self.log(f"Sent firmware info: size={firmware_size}, crc=0x{firmware_crc:08X}")

            # Check connection after Info write
            print(f"DEBUG: Connection status after Info write: {self.client.is_connected}")

            # Small delay to ensure firmware info is processed
            await asyncio.sleep(0.5)

            # Step 2: Send OTA_CMD_START to control characteristic
            cmd_data = bytes([OTA_CMD_START])  # Just the command byte

            # Debug: Check connection before Control write
            print(f"DEBUG: Connection status before Control write: {self.client.is_connected}")
            print(f"DEBUG: About to write {len(cmd_data)} bytes to Control characteristic")

            await self.client.write_gatt_char(OTA_CONTROL_CHAR_UUID, cmd_data)
            print(f"✅ Successfully wrote start command")
            self.log(f"Sent start command: slot={slot}")

            # Small delay to ensure command is processed
            await asyncio.sleep(0.1)

            self.log(f"OTA started successfully")
            return True
        except Exception as e:
            self.log(f"Failed to send start command: {e}", "ERROR")
            return False
    
    async def send_chunk(self, sequence: int, chunk_data: bytes) -> bool:
        """Send firmware chunk with retries"""

        # Create chunk header matching ota_chunk_header_t: [sequence:4][size:2][reserved:2]
        header = struct.pack('<IHH', sequence, len(chunk_data), 0)  # reserved = 0
        full_chunk = header + chunk_data

        for attempt in range(MAX_RETRIES):
            # Check if same version was already detected - stop immediately
            if self.same_version_detected:
                return False

            try:
                # Check if still connected before sending
                if not self.client.is_connected:
                    # Don't reconnect if OTA transfer is already complete
                    if self.ota_transfer_complete:
                        self.log(f"OTA transfer already complete, not reconnecting for chunk {sequence}")
                        return True

                    self.log(f"Connection lost before sending chunk {sequence}, attempting reconnection...")
                    if await self.reconnect():
                        # Restart OTA after reconnection
                        self.log("Restarting OTA after reconnection...")
                        if not await self.send_start_command():
                            return False
                    else:
                        return False

                # Clear ACK event before sending
                self.ack_received.clear()

                await self.client.write_gatt_char(OTA_DATA_CHAR_UUID, full_chunk)

                # Check if OTA completed (ESP32 will restart and disconnect)
                if self.ota_completed:
                    self.log(f"OTA completed after chunk {sequence}")
                    return True

                ack_code, ack_sequence = await self.wait_for_ack(sequence)

                if ack_code == OTA_STATUS_ACK and ack_sequence == sequence:
                    # No delay for maximum speed
                    return True
                elif ack_code == OTA_STATUS_SUCCESS:
                    # Final chunk acknowledged, ready for END command
                    self.log(f"All chunks transferred successfully, ready for END command")
                    return True
                elif ack_code == OTA_STATUS_NACK:
                    self.log(f"Chunk {sequence} NACK received (attempt {attempt + 1})", "WARNING")
                    # No delay on NACK - retry immediately
                    continue
                elif ack_code == OTA_STATUS_ERROR and self.same_version_detected:
                    # Same version detected, stop transfer immediately
                    return False
                else:
                    self.log(f"Chunk {sequence} failed with ACK code: {ack_code}", "ERROR")
                    return False

            except Exception as e:
                # Check if connection was lost
                if not self.client.is_connected:
                    # If this might be the final chunk and connection was lost,
                    # it could mean OTA completed and ESP32 restarted
                    if "Operation aborted" in str(e) or "WinError -2147467260" in str(e):
                        self.log("ESP32 completed OTA and restarted during final chunk transfer")
                        self.ota_completed = True
                        self.ota_transfer_complete = True  # Set flag to prevent reconnection
                        return True
                    else:
                        # Don't reconnect if OTA transfer is already complete
                        if self.ota_transfer_complete:
                            self.log("OTA transfer already complete, not reconnecting after connection loss")
                            return True

                        self.log(f"Connection lost during chunk {sequence} transfer", "WARNING")
                        if attempt < MAX_RETRIES - 1:
                            self.log("Attempting reconnection and OTA restart...")
                            if await self.reconnect():
                                # Restart OTA from beginning after reconnection
                                if await self.send_start_command():
                                    self.log("OTA restarted after reconnection, continuing from beginning...")
                                    # Return False to restart transfer from chunk 0
                                    return False
                        return False
                else:
                    # Don't show verbose error messages if same version was detected
                    if not self.same_version_detected:
                        self.log(f"Failed to send chunk {sequence} (attempt {attempt + 1}): {e}", "ERROR")

                if attempt < MAX_RETRIES - 1:
                    await asyncio.sleep(0.1)
        
        return False

    async def end_ota(self) -> bool:
        """Send END command to ESP32 to trigger verification and restart"""
        self.log("Sending END command to ESP32...")

        # Set flag to prevent reconnection attempts after this point
        self.ota_transfer_complete = True

        try:
            # Send END command - ESP32 will process it and restart immediately
            await self.client.write_gatt_char(OTA_CONTROL_CHAR_UUID, struct.pack('<B', OTA_CMD_END))
            self.log("END command sent successfully")

            # Try to wait for success notification, but ESP32 might restart before sending it
            try:
                ack_code, _ = await self.wait_for_ack(0, timeout=5.0)  # Short timeout since restart is expected
                if ack_code == OTA_STATUS_SUCCESS:
                    self.log("Received success confirmation from ESP32")
            except asyncio.TimeoutError:
                # This is normal - ESP32 likely restarted before sending ACK
                pass

        except Exception:
            # Connection lost during END command - this is expected when ESP32 restarts
            pass

        # Give ESP32 time to complete verification and restart
        await asyncio.sleep(1.5)  # Reduced from 2.0s, but still safe

        # OTA is considered successful if we got this far
        elapsed = time.time() - self.start_time if self.start_time else 0
        print("OTA completed successfully!")
        print(f" Total time: {elapsed:.1f} seconds")
        print("ESP32 has restarted with new firmware.")
        return True



    async def transfer_firmware(self, firmware_path: str, slot: int = 0) -> bool:
        """Transfer firmware file to ESP32"""
        if not os.path.exists(firmware_path):
            self.log(f"Firmware file not found: {firmware_path}", "ERROR")
            return False

        # Read firmware file
        with open(firmware_path, 'rb') as f:
            firmware_data = f.read()

        firmware_size = len(firmware_data)
        firmware_crc = zlib.crc32(firmware_data) & 0xFFFFFFFF

        print(f"Firmware: {firmware_path}")
        print(f"Size: {firmware_size} bytes ({firmware_size/1024:.1f} KB)")
        print(f"CRC32: 0x{firmware_crc:08X} (for reference only)")

        # Check if resume is possible
        status = await self.read_status()
        if status and status['resume_mode'] and status['target_slot'] == slot:
            self.log(f"Resume possible from sequence {status['last_sequence']}")

            # Send resume command
            if await self.send_control_command(OTA_CMD_RESUME):
                ack_code, resume_sequence = await self.wait_for_ack(0)
                if ack_code == OTA_ACK_RESUME:
                    self.log(f"Resuming from sequence {resume_sequence}")
                    start_sequence = resume_sequence + 1
                else:
                    self.log("Resume failed, starting fresh transfer")
                    if not await self.start_ota(slot, firmware_size, firmware_crc):
                        return False
                    start_sequence = 1
            else:
                return False
        else:
            # Start fresh transfer
            if not await self.start_ota(slot, firmware_size, firmware_crc):
                return False
            start_sequence = 1

        # Send firmware in chunks
        chunk_size = MAX_CHUNK_SIZE - HEADER_SIZE
        total_chunks = (firmware_size + chunk_size - 1) // chunk_size

        print(f"Starting OTA transfer: {total_chunks} chunks of {chunk_size} bytes each")
        self.start_time = time.time()

        for i in range(start_sequence - 1, total_chunks):
            sequence = i + 1
            start_offset = i * chunk_size
            end_offset = min(start_offset + chunk_size, firmware_size)
            chunk_data = firmware_data[start_offset:end_offset]

            self.log(f"Sending chunk {sequence}/{total_chunks} ({len(chunk_data)} bytes)")

            if not await self.send_chunk(sequence, chunk_data):
                # Check if same version was detected
                if self.same_version_detected:
                    return False  # Error message already shown in notification_handler
                # Check if OTA was already completed (ESP32 restarted)
                elif self.ota_completed:
                    # Calculate final stats
                    elapsed = time.time() - self.start_time if self.start_time else 0
                    print(f"100% Complete! All {total_chunks} chunks transferred successfully")
                    print(f"Total time: {elapsed:.1f}s | Average rate: {total_chunks/elapsed:.1f} chunks/s")
                    return True
                else:
                    self.log(f"Failed to send chunk {sequence}", "ERROR")
                    return False

            # Show progress every 10% or every 100 chunks
            progress = (sequence * 100) // total_chunks
            if progress != self.last_progress_percent and (progress % 10 == 0 or sequence == total_chunks):
                elapsed = time.time() - self.start_time if self.start_time else 0
                rate = sequence / elapsed if elapsed > 0 else 0
                eta = (total_chunks - sequence) / rate if rate > 0 else 0
                print(f"Progress: {progress}% ({sequence}/{total_chunks} chunks) | "
                      f"Rate: {rate:.1f} chunks/s | ETA: {eta:.0f}s")
                self.last_progress_percent = progress

        # Check if OTA was already completed during the transfer
        if self.ota_completed:
            elapsed = time.time() - self.start_time if self.start_time else 0
            print(f" 100% Complete! All {total_chunks} chunks transferred successfully")
            print(f"Total time: {elapsed:.1f}s | Average rate: {total_chunks/elapsed:.1f} chunks/s")
            return True

        # Show final completion
        elapsed = time.time() - self.start_time if self.start_time else 0
        print(f"100% Complete! All {total_chunks} chunks transferred successfully")
        print(f"Total time: {elapsed:.1f}s | Average rate: {total_chunks/elapsed:.1f} chunks/s")

        # Minimal delay before END command
        await asyncio.sleep(0.5)

        # Send END command to trigger ESP32 verification and restart (standard ESP-IDF flow)
        return await self.end_ota()

    async def abort_transfer(self) -> bool:
        """Abort current OTA transfer"""
        if not await self.send_control_command(OTA_CMD_ABORT):
            return False

        ack_code, _ = await self.wait_for_ack(0)
        self.log("OTA transfer aborted")
        return True


async def main():
    parser = argparse.ArgumentParser(description="NimBLE OTA Client for ESP32")
    parser.add_argument("firmware", nargs='?', help="Path to firmware binary file")
    parser.add_argument("-a", "--address", help="ESP32 BLE device address")
    parser.add_argument("-s", "--slot", type=int, choices=[0, 1], default=0,
                       help="Target OTA slot (0 or 1)")
    parser.add_argument("-v", "--verbose", action="store_true",
                       help="Enable verbose logging")
    parser.add_argument("--scan", action="store_true",
                       help="Scan for devices and exit")
    parser.add_argument("--status", action="store_true",
                       help="Read device status and exit")
    parser.add_argument("--abort", action="store_true",
                       help="Abort current transfer and exit")

    args = parser.parse_args()

    client = NimBLEOTAClient(args.address, args.verbose)

    try:
        if args.scan:
            devices = await client.scan_for_devices()
            if devices:
                print("\nFound target devices:")
                for device in devices:
                    print(f"  {device.name} - {device.address}")
            else:
                print("No target devices found")
            return

        # Check if firmware is required but not provided
        if not args.scan and not args.status and not args.abort and not args.firmware:
            print("Error: firmware file is required for OTA transfer")
            print("Use --scan to scan for devices without firmware")
            return

        if not await client.connect():
            print("Failed to connect to device")
            return

        if args.status:
            status = await client.read_status()
            if status:
                print(f"\nDevice Status:")
                print(f"  State: {status['state']}")
                print(f"  Progress: {status['progress']}%")
                print(f"  Received: {status['received_bytes']}/{status['total_size']} bytes")
                print(f"  Last sequence: {status['last_sequence']}")
                print(f"  Target slot: {status['target_slot']}")
                print(f"  Resume mode: {status['resume_mode']}")
            return

        if args.abort:
            await client.abort_transfer()
            return

        # Transfer firmware
        success = await client.transfer_firmware(args.firmware, args.slot)
        if success:
            print("Firmware transfer completed successfully!")
        else:
            if client.same_version_detected:
                # Exit with code 2 for same version conflict
                sys.exit(2)
            else:
                print("Firmware transfer failed!")
                sys.exit(1)

    except KeyboardInterrupt:
        print("\nTransfer interrupted by user")
        await client.abort_transfer()

    finally:
        await client.disconnect()


if __name__ == "__main__":
    # Example usage:
    # python nimble_ota_client.py firmware.bin --verbose
    # python nimble_ota_client.py --scan
    # python nimble_ota_client.py --status -a AA:BB:CC:DD:EE:FF
    asyncio.run(main())
