#!/usr/bin/env python3
"""
OTA Security Configuration Helper
Choose between security bypass and full bonding for OTA operations
"""

import os
import sys

def show_security_options():
    """Display available security options"""
    print("🔐 ESP32 OTA Security Configuration")
    print("=" * 50)
    print()
    print("Choose your OTA security approach:")
    print()
    print("1. 🚀 BYPASS SECURITY (Recommended for OTA)")
    print("   - Fastest connection")
    print("   - No pairing required")
    print("   - Uses ESP32 OTA security bypass")
    print("   - Best for firmware updates")
    print()
    print("2. 🔒 FULL SECURITY (Production)")
    print("   - Proper BLE bonding")
    print("   - Persistent pairing")
    print("   - Matches ESP32 security config")
    print("   - Best for secure applications")
    print()
    print("3. 🧪 TEST SECURITY")
    print("   - Test bonding functionality")
    print("   - Verify security implementation")
    print("   - Debug security issues")
    print()

def run_ota_bypass():
    """Run OTA with security bypass (default)"""
    print("🚀 Running OTA with Security Bypass...")
    print("Command: python flash_firmware_select.bat")
    print()
    print("This uses the ESP32 OTA security bypass mode:")
    print("- No BLE pairing required")
    print("- ESP32 disables security during OTA")
    print("- Fastest and most reliable for firmware updates")
    print()
    os.system("python flash_firmware_select.bat")

def run_ota_security():
    """Run OTA with full security"""
    print("🔒 Running OTA with Full Security...")
    
    # Check if firmware file exists
    firmware_files = [f for f in os.listdir('.') if f.endswith('.bin')]
    if not firmware_files:
        print("❌ No firmware files found!")
        print("Please ensure firmware .bin file is in current directory")
        return
    
    firmware_file = firmware_files[0]
    print(f"Using firmware: {firmware_file}")
    print()
    print("This uses full BLE security:")
    print("- Proper BLE bonding")
    print("- Persistent pairing")
    print("- Encrypted connection")
    print()
    
    cmd = f"python nimble_ota_client.py --security --verbose {firmware_file}"
    print(f"Command: {cmd}")
    print()
    os.system(cmd)

def test_security():
    """Test security implementation"""
    print("🧪 Testing BLE Security Implementation...")
    print()
    print("This will test:")
    print("- BLE connection")
    print("- Pairing/bonding")
    print("- Service discovery")
    print("- Characteristic access")
    print()
    os.system("python test_ble_security.py")

def main():
    """Main configuration menu"""
    while True:
        show_security_options()
        
        try:
            choice = input("Enter your choice (1-3, or 'q' to quit): ").strip()
            
            if choice.lower() == 'q':
                print("👋 Goodbye!")
                break
            elif choice == '1':
                run_ota_bypass()
                break
            elif choice == '2':
                run_ota_security()
                break
            elif choice == '3':
                test_security()
                break
            else:
                print("❌ Invalid choice. Please enter 1, 2, 3, or 'q'")
                print()
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
