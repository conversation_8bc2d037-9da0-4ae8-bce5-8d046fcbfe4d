#!/usr/bin/env python3
"""
ESP32 Connection Test Script
Tests all possible connection methods with ESP32
"""

import asyncio
import sys
import time
try:
    import serial
    import serial.tools.list_ports
    SERIAL_AVAILABLE = True
except ImportError:
    SERIAL_AVAILABLE = False
    print("⚠️ pyserial not installed. Serial testing disabled.")

try:
    from bleak import BleakScanner, BleakClient
    BLE_AVAILABLE = True
except ImportError:
    BLE_AVAILABLE = False
    print("⚠️ bleak not installed. BLE testing disabled.")

async def test_ble_scan():
    """Test BLE scanning for ESP32 devices"""
    print("🔍 Scanning for BLE devices...")
    
    if not BLE_AVAILABLE:
        print("❌ BLE not available - install bleak: pip install bleak")
        return []
    
    try:
        devices = await BleakScanner.discover(timeout=10)
        esp32_devices = []
        
        print(f"Found {len(devices)} BLE devices:")
        for device in devices:
            if device.name:
                if any(keyword in device.name.upper() for keyword in ['MR', 'ESP32', 'CT', 'COP']):
                    print(f"✅ ESP32 Device: {device.name} ({device.address})")
                    esp32_devices.append(device)
                else:
                    print(f"   Other: {device.name} ({device.address})")
            else:
                print(f"   Unknown: {device.address}")
        
        return esp32_devices
        
    except Exception as e:
        print(f"❌ BLE scan failed: {e}")
        return []

async def test_ble_connection(device_address):
    """Test BLE connection to specific ESP32"""
    print(f"🔗 Testing BLE connection to {device_address}...")
    
    try:
        async with BleakClient(device_address, timeout=20) as client:
            print(f"✅ Connected to {device_address}")
            
            # Test service discovery
            services = await client.get_services()
            print(f"📋 Found {len(services)} services:")
            
            ota_service_found = False
            for service in services:
                service_uuid = str(service.uuid).lower()
                print(f"   Service: {service.uuid}")
                
                if service_uuid == "def09abc-5678-1234-def0-9abc56781234":
                    ota_service_found = True
                    print("   ✅ OTA Service found!")
                    
                    for char in service.characteristics:
                        print(f"      Characteristic: {char.uuid} ({char.properties})")
            
            if ota_service_found:
                print("🎉 ESP32 OTA service is accessible!")
                return True
            else:
                print("⚠️ OTA service not found")
                return False
                
    except Exception as e:
        print(f"❌ BLE connection failed: {e}")
        return False

def test_serial_ports():
    """Test serial port detection"""
    print("🔌 Scanning for serial ports...")
    
    if not SERIAL_AVAILABLE:
        print("❌ Serial not available - install pyserial: pip install pyserial")
        return []
    
    try:
        ports = serial.tools.list_ports.comports()
        esp32_ports = []
        
        print(f"Found {len(ports)} serial ports:")
        for port in ports:
            print(f"   {port.device} - {port.description}")
            
            # Look for common ESP32 USB-to-serial chips
            if any(chip in port.description.upper() for chip in ['CP210', 'FTDI', 'CH340', 'USB-SERIAL']):
                print(f"   ✅ Possible ESP32 port: {port.device}")
                esp32_ports.append(port.device)
        
        return esp32_ports
        
    except Exception as e:
        print(f"❌ Serial port scan failed: {e}")
        return []

def test_serial_connection(port):
    """Test serial connection to ESP32"""
    print(f"📡 Testing serial connection to {port}...")
    
    try:
        ser = serial.Serial(port, 115200, timeout=2)
        time.sleep(1)  # Wait for connection
        
        print(f"✅ Serial connection established on {port}")
        
        # Try to send a test command
        test_command = "BLE_START_ADV:Test Device\n"
        ser.write(test_command.encode())
        
        # Wait for response
        time.sleep(1)
        if ser.in_waiting > 0:
            response = ser.read(ser.in_waiting).decode('utf-8', errors='ignore')
            print(f"📨 ESP32 Response: {response.strip()}")
        else:
            print("⚠️ No response from ESP32")
        
        ser.close()
        return True
        
    except Exception as e:
        print(f"❌ Serial connection failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🧪 ESP32 Connection Test Suite")
    print("=" * 50)
    
    # Test 1: BLE Scanning
    print("\n1️⃣ Testing BLE Scanning...")
    esp32_devices = await test_ble_scan()
    
    # Test 2: BLE Connection
    if esp32_devices:
        print(f"\n2️⃣ Testing BLE Connection...")
        for device in esp32_devices[:1]:  # Test first device only
            success = await test_ble_connection(device.address)
            if success:
                print(f"🎉 BLE connection to {device.name} successful!")
                break
    else:
        print("\n2️⃣ Skipping BLE connection test - no ESP32 devices found")
    
    # Test 3: Serial Port Detection
    print(f"\n3️⃣ Testing Serial Ports...")
    esp32_ports = test_serial_ports()
    
    # Test 4: Serial Connection
    if esp32_ports:
        print(f"\n4️⃣ Testing Serial Connection...")
        for port in esp32_ports[:1]:  # Test first port only
            success = test_serial_connection(port)
            if success:
                print(f"🎉 Serial connection to {port} successful!")
                break
    else:
        print("\n4️⃣ Skipping serial connection test - no ESP32 ports found")
    
    # Summary
    print(f"\n📊 Connection Test Summary:")
    print(f"   BLE Devices Found: {len(esp32_devices)}")
    print(f"   Serial Ports Found: {len(esp32_ports)}")
    
    if esp32_devices or esp32_ports:
        print("✅ ESP32 connection methods available!")
        print("\nNext steps:")
        if esp32_devices:
            print(f"   - Use BLE: python flash_firmware_select.bat")
        if esp32_ports:
            print(f"   - Use Serial: Connect to {esp32_ports[0]} at 115200 baud")
    else:
        print("❌ No ESP32 connections found!")
        print("\nTroubleshooting:")
        print("   - Ensure ESP32 is powered on")
        print("   - Check USB cable connection")
        print("   - Install drivers: pip install bleak pyserial")
        print("   - Enable BLE advertising on ESP32")

if __name__ == "__main__":
    asyncio.run(main())
