/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : ble_response_handler.c
* @version 		  : 1.0.5
* @brief Handle the BLE Response 
* @details Handle the BLE Response 
*****************************************************************************
* @version 1.0.1                                        				Date : 09/07/2025
* Modified based on new packet type
********************************************************************************************
* @version 1.0.2                                        				Date : 18/07/2025
* Modified attribute handle name
********************************************************************************************
* @version 1.0.3                                        				Date : 21/07/2025
* Added comprehensive function header for ble_response_handler()
* Added send_ble_status_to_nexus() for sending status connected disconnected.
********************************************************************************************
* @version 1.0.4                                        				Date : 25/07/2025
* Initialize UART variables after sending Read Configuration
********************************************************************************************
* @version 1.0.5                                        				Date : 26/07/2025
* Modified for Data Offset Approach
********************************************************************************************/
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include "host/ble_hs.h"
#include "esp_log.h"
#include "ble_response_handler.h"
#include "crc.h"
#include "utility.h"
#include "uart_handler.h"

/* DEFINES */
#define BUF_SIZE (1024)
#define FW_FILE_CHUNK (20)
#define BUFFER_SIZE (FW_FILE_CHUNK * BUF_SIZE) // 256KB
#define CHUNK_SIZE (500)

/* VARIABLES */
static uint32_t data_read_length = 0;
static uint32_t fw_file_offset = 0;
static uint32_t fw_file_length = 0;
static uint8_t global_buffer[BUFFER_SIZE];
static uint32_t uart_write_index = 0;
extern uint16_t config_att_handle;
int32_t current_offset = 0;
uint32_t sent = 0;
static uint16_t connection_hndl = 0;

/* FUNCTION PROTOTYPES */
void send_config_file(void);

/**
 * @brief Resets all buffer-related variables to initial state.
 *
 * This function clears and resets buffer management variables
 * such as buffer pointers, counters, data length trackers, and
 * state machine statuses. It is typically used at the start or
 * end of a communication session or after an error recovery.
 *
 * @note This function only resets control variables; if buffer
 * memory needs to be cleared, it should be handled separately.
 *
 * @return None.
 */
void reset_buffer_variables(void)
{
	uart_write_index = 0;
	sent = 0;
	current_offset = 0;
}

/**
 * @brief Sets the expected data read length.
 *
 * This function configures the length of data to be read in the
 * next read operation. It is typically used in protocols where
 * the amount of incoming data varies and needs to be set dynamically
 * before initiating a read.
 *
 * @param length   The number of bytes to read in the next read
 *                 or data reception operation.
 *
 * @return None.
 */
void set_data_read_length(uint32_t length)
{
#ifdef DEBUG_CMD
	ESP_LOGI("SET DATA_LEN: ", "%ld", length);
#endif
	data_read_length = length;
}

/**
 * @brief Retrieves the current expected data read length.
 *
 * This function returns the number of bytes previously configured
 * for the next data read operation using set_data_read_length().
 * @param None
 * @return  The expected data read length in bytes.
 */
uint32_t get_data_read_length(void)
{
	return data_read_length;
}

/**
 * @brief Sets the expected FW File data read length.
 *
 * This function configures the length of data to be read in the
 * next read operation. It is typically used in protocols where
 * the amount of incoming data varies and needs to be set dynamically
 * before initiating a read.
 *
 * @param length   The number of bytes to read in the next read
 *                 or data reception operation.
 *
 * @return None.
 */
void set_fw_file_offset_length(uint32_t offset,  uint32_t length) {
#ifdef DEBUG_CMD
	ESP_LOGI("SET DATA_LEN: ", "%ld", length);
#endif
	fw_file_offset = offset;
	fw_file_length = length;
}

/**
 * @brief Retrieves the current expected data read length.
 *
 * This function returns the number of bytes previously configured
 * for the next data read operation using set_data_read_length().
 * @param None
 * @return  The expected data read length in bytes.
 */
uint32_t get_fw_file_offset(void) {
	return fw_file_offset;
}

uint32_t get_fw_file_length(void) {
	return fw_file_length;
}

/**
 * @brief Stores the configuration file data into internal buffer.
 *
 * This function saves the provided configuration data into an
 * internal buffer. It is typically
 * called when receiving a configuration file over UART from Nexus
 *
 * @param data    Pointer to the data buffer containing the configuration file.
 * @param length  Length of the configuration data in bytes.
 *
 * @return None.
 */
void store_config_file(uint8_t *data, uint32_t length)
{
	memcpy(global_buffer + uart_write_index, data, length);
#ifdef DEBUG_CMD
	ESP_LOGI("UART_WRITE_INDEX", "%ld", uart_write_index);
#endif
	uart_write_index += length;
#ifdef DEBUG_CMD
	ESP_LOGI("BYTES RECEIVED: ", "%ld", uart_write_index);
#endif

	if (uart_write_index >= get_data_read_length())
	{
#ifdef DEBUG_CMD
		ESP_LOGI("READ COMPLETE", "SEND NOTIFICATION");
#endif
		send_config_file();
	}
}

/**
 * @brief Sets the current BLE connection handle.
 *
 * This function saves the connection handle associated with
 * an active BLE connection. It is typically called during
 * connection events to store the handle for future communication
 * such as notifications, indications, or connection management.
 *
 * @param hndl   The BLE connection handle.
 *
 * @return None.
 */
void set_connection_handle(uint16_t hndl)
{
	connection_hndl = hndl;
}

/**
 * @brief Retrieves the current BLE connection handle.
 *
 * This function returns the connection handle previously stored
 * using set_connection_handle(). The connection handle is used
 * for sending BLE notifications, indications, or managing the
 * active BLE connection.
 *
 * @return The currently stored BLE connection handle.
 *         Returns 0xFFFF if no active connection exists.
 */
uint16_t get_connection_handle(void)
{
	return connection_hndl;
}

/**
 * @brief Provides access to the global data buffer.
 *
 * This function returns a pointer to the global data buffer and
 * optionally provides the current valid data length stored in the buffer.
 *
 * @param[out] length   Pointer to a variable where the buffer length
 *                      will be stored. Pass NULL if length is not needed.
 *
 * @return Pointer to the global data buffer.
 */
uint8_t *global_buffer_read(uint32_t *length)
{
	uint32_t remaining = get_data_read_length() - current_offset;
	uint32_t prev_offset = 0;
	uint32_t len = remaining > CHUNK_SIZE ? CHUNK_SIZE : remaining;
#ifdef DEBUG_CMD
	ESP_LOGI("READ: ", "Sent: %ld", sent += *length);
#endif

	prev_offset = current_offset;
	*length = len;
	current_offset += len;

	if (current_offset >= get_data_read_length())
	{
		current_offset = 0;
	}

	return &global_buffer[prev_offset];
}

/**
 * @brief Sends the stored configuration file to the connected client.
 *
 * This function transmits the configuration file data from the
 * internal buffer to the client over BLE (via notifications).
 * It handles chunked transmission if the file size exceeds
 * the MTU or buffer limits.
 *
 * @note Requires an active connection handle. The function typically
 * checks connection validity before attempting transmission.
 *
 * @return None.
 */
void send_config_file(void)
{
	uint8_t temp[517];
	uint32_t crc_calc;
	uint32_t length;
	uint16_t conn_hndl = 0;
	struct os_mbuf *om;
	conn_hndl = get_connection_handle();
	if (conn_hndl == 0)
	{
		return;
	}

	while (1)
	{
		memset(temp, '\0', sizeof(temp));

		temp[0] = START_BYTE;
		temp[1] = BLE_READ_CONFIG_RESPONSE;

		const uint8_t *resp = global_buffer_read(&length);
		u32_to_byte_array_little_endian(&temp[2], length);
		memcpy(&temp[HEADER_SIZE], resp, (length));
		crc_calc = crc32(temp, (length) + HEADER_SIZE);
		u32_to_byte_array_little_endian(&temp[HEADER_SIZE + (length)], crc_calc);
		length += 10;
#ifdef DEBUG_CMD
		ESP_LOGI("NOTIFY DATA LENGTH: ", "%ld", length);
#endif
		om = ble_hs_mbuf_from_flat(&temp, length);
		ble_gattc_notify_custom(conn_hndl,
								config_att_handle,
								om);
		if (length < 510)
		{
			initialize_uart_vars();
			uart_write_index = 0;
			break;
		}
		vTaskDelay(pdMS_TO_TICKS(200));
	}
}

/**
 * @brief Sends BLE connection/disconnection status from ESP32 to Nexus board via UART.
 *
 * This function takes BLE status data (connected/disconnected events) and formats
 * it into a proper UART frame for transmission to the Nexus board. The frame includes
 * start byte, packet type, length, payload, and CRC32 checksum. Non-blocking
 * transmission is used to avoid interfering with the BLE stack operations.
 *
 * Frame format: [START_BYTE][PACKET_TYPE][LENGTH(4 bytes)][PAYLOAD][CRC32(4 bytes)]
 *
 * @param packet_type  The type of BLE status packet (BLE_CONNECTED/BLE_DISCONNECTED)
 * @param payload      Pointer to the status payload data buffer
 * @param payload_len  Length of the status payload data in bytes
 *
 * @return None
 *
 * @note This function uses little-endian format for length and CRC fields
 * @note Uses non-blocking UART transmission to prevent BLE stack interference
 * @note Specifically designed for ESP32 to Nexus board communication
 */
void send_ble_status_to_nexus(uint8_t packet_type, uint8_t *payload, uint32_t payload_len)
{
    uint8_t uart_frame[256];
    uint32_t frame_len = 0;
    uint32_t crc_calc = 0;

    uart_frame[START_BYTE_INDEX] = START_BYTE;
    uart_frame[PACKET_TYPE_INDEX] = packet_type;
    u32_to_byte_array_little_endian(&uart_frame[LENGTH_START_INDEX], payload_len);

    if (payload_len > 0)
    {
        memcpy(&uart_frame[HEADER_SIZE], payload, payload_len);
    }

    frame_len = HEADER_SIZE + payload_len;
    crc_calc = crc32(uart_frame, frame_len);
    u32_to_byte_array_little_endian(&uart_frame[frame_len], crc_calc);
    frame_len += CRC_LENGTH;

    // Use non-blocking UART send to avoid interfering with BLE stack
    write_data(uart_frame, frame_len);
}
