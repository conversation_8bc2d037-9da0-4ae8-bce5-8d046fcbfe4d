[0/1] Re-running CMake...
-- ccache will be used for faster recompilation
-- Building ESP-IDF components for target esp32s3
NOTICE: Using component placed at C:\Users\<USER>\esp\v5.4.1\esp-idf\examples\bluetooth\nimble\common\nimble_peripheral_utils for dependency "nimble_peripheral_utils", specified in D:\BLE\Bitbucket\Feature_ble_fw_esp32\APP_ESP32\main\idf_component.yml
NOTICE: Processing 3 dependencies:
NOTICE: [1/3] joltwallet/littlefs (1.14.8)
NOTICE: [2/3] nimble_peripheral_utils (*) (C:\Users\<USER>\esp\v5.4.1\esp-idf\examples\bluetooth\nimble\common\nimble_peripheral_utils)
NOTICE: [3/3] idf (5.4.1)
-- Project sdkconfig file D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/sdkconfig
Loading defaults file D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/sdkconfig.defaults...
warning: unknown kconfig symbol 'BTDM_CTRL_MODE_BLE_ONLY' assigned to 'y' in D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/sdkconfig.defaults
warning: unknown kconfig symbol 'BTDM_CTRL_MODE_BR_EDR_ONLY' assigned to 'n' in D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/sdkconfig.defaults
warning: unknown kconfig symbol 'BTDM_CTRL_MODE_BTDM' assigned to 'n' in D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/sdkconfig.defaults
-- Compiler supported targets: xtensa-esp-elf
-- App "Vantage_nxESP32_Int_Rel_1_2_0_0" version: Vantage_Bin_nxESP32_1_2_0_0
-- Adding linker script D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.bt_funcs.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- Components: app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_security esp_system esp_timer esp_vfs_console esp_wifi espcoredump esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 joltwallet__littlefs json log lwip main mbedtls mqtt newlib nimble_peripheral_utils nvs_flash nvs_sec_provider openthread partition_table perfmon protobuf-c protocomm pthread rt sdmmc soc spi_flash spiffs tcp_transport touch_element ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant xtensa
-- Component paths: C:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_trace C:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_update C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt C:/Users/<USER>/esp/v5.4.1/esp-idf/components/cmock C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console C:/Users/<USER>/esp/v5.4.1/esp-idf/components/cxx C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver C:/Users/<USER>/esp/v5.4.1/esp-idf/components/efuse C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp-tls C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_app_format C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_bootloader_format C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_coex C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_common C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ana_cmpr C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_cam C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_dac C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gpio C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gptimer C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2c C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2s C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_isp C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_jpeg C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ledc C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_parlio C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_pcnt C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ppa C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_rmt C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdio C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdm C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdmmc C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdspi C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_spi C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_touch_sens C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_tsens C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_uart C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_usb_serial_jtag C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_event C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_gdbstub C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hid C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_client C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_server C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_ota C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_server C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_local_ctrl C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_mm C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif_stack C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_partition C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_phy C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_pm C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_psram C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_ringbuf C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_security C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_timer C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_vfs_console C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi C:/Users/<USER>/esp/v5.4.1/esp-idf/components/espcoredump C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py C:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal C:/Users/<USER>/esp/v5.4.1/esp-idf/components/heap C:/Users/<USER>/esp/v5.4.1/esp-idf/components/http_parser C:/Users/<USER>/esp/v5.4.1/esp-idf/components/idf_test C:/Users/<USER>/esp/v5.4.1/esp-idf/components/ieee802154 D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/managed_components/joltwallet__littlefs C:/Users/<USER>/esp/v5.4.1/esp-idf/components/json C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mqtt C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib C:/Users/<USER>/esp/v5.4.1/esp-idf/examples/bluetooth/nimble/common/nimble_peripheral_utils C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_sec_provider C:/Users/<USER>/esp/v5.4.1/esp-idf/components/openthread C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table C:/Users/<USER>/esp/v5.4.1/esp-idf/components/perfmon C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protobuf-c C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm C:/Users/<USER>/esp/v5.4.1/esp-idf/components/pthread C:/Users/<USER>/esp/v5.4.1/esp-idf/components/rt C:/Users/<USER>/esp/v5.4.1/esp-idf/components/sdmmc C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spiffs C:/Users/<USER>/esp/v5.4.1/esp-idf/components/tcp_transport C:/Users/<USER>/esp/v5.4.1/esp-idf/components/touch_element C:/Users/<USER>/esp/v5.4.1/esp-idf/components/ulp C:/Users/<USER>/esp/v5.4.1/esp-idf/components/unity C:/Users/<USER>/esp/v5.4.1/esp-idf/components/usb C:/Users/<USER>/esp/v5.4.1/esp-idf/components/vfs C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wear_levelling C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant C:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa
-- Configuring done (27.0s)
-- Generating done (1.9s)
-- Build files have been written to: D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build
[1/1150] Generating ../../ota_data_initial.bin
[2/1150] Generating D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/esp-idf/esp_system/ld/memory.ld linker script...
[3/1150] Generating D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/esp-idf/esp_system/ld/sections.ld.in linker script...
[4/1150] Building C object esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/esp_psram.c.obj
[5/1150] Building C object esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/mmu_psram_flash.c.obj
[6/1150] Building C object esp-idf/esp_psram/CMakeFiles/__idf_esp_psram.dir/esp32s3/esp_psram_impl_octal.c.obj
[7/1150] Building C object esp-idf/esp_https_ota/CMakeFiles/__idf_esp_https_ota.dir/src/esp_https_ota.c.obj
[8/1150] Building C object esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_main.c.obj
[9/1150] Building C object esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_parse.c.obj
[10/1150] Building C object esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_sess.c.obj
[11/1150] Building C object esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_txrx.c.obj
[12/1150] Building C object esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_uri.c.obj
[13/1150] Building C object esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/httpd_ws.c.obj
[14/1150] Building C object esp-idf/esp_http_server/CMakeFiles/__idf_esp_http_server.dir/src/util/ctrl_sock.c.obj
[15/1150] Building C object esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/esp_http_client.c.obj
[16/1150] Building C object esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/lib/http_auth.c.obj
[17/1150] Building C object esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/lib/http_header.c.obj
[18/1150] Generating ../../partition_table/partition-table.bin
Partition table binary generated. Contents:
*******************************************************************************
# ESP-IDF Partition Table

# Name, Type, SubType, Offset, Size, Flags

nvs,data,nvs,0x9000,16K,

otadata,data,ota,0xd000,8K,

phy_init,data,phy,0xf000,4K,

factory,app,factory,0x10000,1M,

ota_0,app,ota_0,0x110000,1M,

ota_1,app,ota_1,0x210000,1M,

storage,data,spiffs,0x310000,12M,

*******************************************************************************
[19/1150] Building C object esp-idf/esp_http_client/CMakeFiles/__idf_esp_http_client.dir/lib/http_utils.c.obj
[20/1150] Building C object esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/transport.c.obj
[21/1150] Building C object esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/transport_ssl.c.obj
[22/1150] Building C object esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/transport_internal.c.obj
[23/1150] Building C object esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/transport_socks_proxy.c.obj
[24/1150] Building C object esp-idf/tcp_transport/CMakeFiles/__idf_tcp_transport.dir/transport_ws.c.obj
[25/1150] Building C object esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/src/gdbstub.c.obj
[26/1150] Building C object esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/src/gdbstub_transport.c.obj
[27/1150] Building C object esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/src/packet.c.obj
[28/1150] Building C object esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/src/port/xtensa/gdbstub_xtensa.c.obj
[29/1150] Building ASM object esp-idf/esp_gdbstub/CMakeFiles/__idf_esp_gdbstub.dir/src/port/xtensa/gdbstub-entry.S.obj
[30/1150] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_oneshot.c.obj
[31/1150] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_common.c.obj
[32/1150] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_cali.c.obj
[33/1150] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_cali_curve_fitting.c.obj
[34/1150] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/deprecated/esp_adc_cal_common_legacy.c.obj
[35/1150] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_continuous.c.obj
[36/1150] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_monitor.c.obj
[37/1150] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/gdma/adc_dma.c.obj
[38/1150] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/adc_filter.c.obj
[39/1150] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/esp32s3/curve_fitting_coefficients.c.obj
[40/1150] Building C object esp-idf/esp_adc/CMakeFiles/__idf_esp_adc.dir/deprecated/esp32s3/esp_adc_cal_legacy.c.obj
[41/1150] Building C object esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/esp_tls.c.obj
[42/1150] Building C object esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/esp-tls-crypto/esp_tls_crypto.c.obj
[43/1150] Building C object esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/esp_tls_error_capture.c.obj
[44/1150] Building C object esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/esp_tls_platform_port.c.obj
[45/1150] Building C object esp-idf/esp-tls/CMakeFiles/__idf_esp-tls.dir/esp_tls_mbedtls.c.obj
[46/1150] Building C object esp-idf/http_parser/CMakeFiles/__idf_http_parser.dir/http_parser.c.obj
[47/1150] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/lib_printf.c.obj
[48/1150] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/mesh_event.c.obj
[49/1150] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/smartconfig.c.obj
[50/1150] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/wifi_default.c.obj
[51/1150] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/wifi_netif.c.obj
[52/1150] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/wifi_default_ap.c.obj
[53/1150] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/esp32s3/esp_adapter.c.obj
[54/1150] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/smartconfig_ack.c.obj
[55/1150] Linking C static library esp-idf\esp_psram\libesp_psram.a
[56/1150] Building C object esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/src/coexist.c.obj
[57/1150] Building C object esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/src/lib_printf.c.obj
[58/1150] Building C object esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/esp32s3/esp_coex_adapter.c.obj
[59/1150] Building C object esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/src/coexist_debug_diagram.c.obj
[60/1150] Building C object esp-idf/esp_coex/CMakeFiles/__idf_esp_coex.dir/src/coexist_debug.c.obj
[61/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/port/os_xtensa.c.obj
[62/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/port/eloop.c.obj
[63/1150] Building C object esp-idf/esp_wifi/CMakeFiles/__idf_esp_wifi.dir/src/wifi_init.c.obj
[64/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/ap_config.c.obj
[65/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/ieee802_1x.c.obj
[66/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/wpa_auth.c.obj
[67/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/wpa_auth_ie.c.obj
[68/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/pmksa_cache_auth.c.obj
[69/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/sta_info.c.obj
[70/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/ieee802_11.c.obj
[71/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/ap/comeback_token.c.obj
[72/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/sae.c.obj
[73/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/dragonfly.c.obj
[74/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/wpa_common.c.obj
[75/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/bitfield.c.obj
[76/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/aes-siv.c.obj
[77/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha256-kdf.c.obj
[78/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/ccmp.c.obj
[79/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/aes-gcm.c.obj
[80/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/crypto_ops.c.obj
[81/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/dh_group5.c.obj
[82/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/dh_groups.c.obj
[83/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/ms_funcs.c.obj
[84/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha1-tlsprf.c.obj
[85/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha256-tlsprf.c.obj
[86/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha384-tlsprf.c.obj
[87/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha256-prf.c.obj
[88/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha1-prf.c.obj
[89/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha384-prf.c.obj
[90/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/md4-internal.c.obj
[91/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/sha1-tprf.c.obj
[92/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_common/eap_wsc_common.c.obj
[93/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/ieee802_11_common.c.obj
[94/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/chap.c.obj
[95/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap.c.obj
[96/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_common.c.obj
[97/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_mschapv2.c.obj
[98/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_peap.c.obj
[99/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_peap_common.c.obj
[100/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_tls.c.obj
[101/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_tls_common.c.obj
[102/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_ttls.c.obj
[103/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/mschapv2.c.obj
[104/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_fast.c.obj
[105/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_fast_common.c.obj
[106/1150] Linking C static library esp-idf\esp_https_ota\libesp_https_ota.a
[107/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/eap_peer/eap_fast_pac.c.obj
[108/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/rsn_supp/pmksa_cache.c.obj
[109/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/rsn_supp/wpa.c.obj
[110/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/rsn_supp/wpa_ie.c.obj
[111/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/base64.c.obj
[112/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/common.c.obj
[113/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/ext_password.c.obj
[114/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/uuid.c.obj
[115/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/wpabuf.c.obj
[116/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/wpa_debug.c.obj
[117/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/utils/json.c.obj
[118/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps.c.obj
[119/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_attr_build.c.obj
[120/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_attr_parse.c.obj
[121/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_attr_process.c.obj
[122/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_common.c.obj
[123/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_dev_attr.c.obj
[124/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/wps/wps_enrollee.c.obj
[125/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/common/sae_pk.c.obj
[126/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_eap_client.c.obj
[127/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_wpa2_api_port.c.obj
[128/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_wpa_main.c.obj
[129/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_wpas_glue.c.obj
[130/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_common.c.obj
[131/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_wps.c.obj
[132/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_wpa3.c.obj
[133/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_owe.c.obj
[134/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/esp_hostap.c.obj
[135/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/tls_mbedtls.c.obj
[136/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/fastpbkdf2.c.obj
[137/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/crypto_mbedtls.c.obj
[138/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/crypto_mbedtls-bignum.c.obj
[139/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/crypto_mbedtls-rsa.c.obj
[140/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/esp_supplicant/src/crypto/crypto_mbedtls-ec.c.obj
[141/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/rc4.c.obj
[142/1150] Linking C static library esp-idf\esp_http_server\libesp_http_server.a
[143/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/des-internal.c.obj
[144/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/aes-wrap.c.obj
[145/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/aes-unwrap.c.obj
[146/1150] Building C object esp-idf/wpa_supplicant/CMakeFiles/__idf_wpa_supplicant.dir/src/crypto/aes-ccm.c.obj
[147/1150] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/esp_netif_handlers.c.obj
[148/1150] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/esp_netif_objects.c.obj
[149/1150] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/esp_netif_defaults.c.obj
[150/1150] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/esp_netif_lwip.c.obj
[151/1150] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/esp_netif_sntp.c.obj
[152/1150] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/esp_netif_lwip_defaults.c.obj
[153/1150] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/netif/wlanif.c.obj
[154/1150] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/netif/ethernetif.c.obj
[155/1150] Building C object esp-idf/esp_netif/CMakeFiles/__idf_esp_netif.dir/lwip/netif/esp_pbuf_ref.c.obj
[156/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/apps/sntp/sntp.c.obj
[157/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/api_lib.c.obj
[158/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/api_msg.c.obj
[159/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/err.c.obj
[160/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/if_api.c.obj
[161/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/netbuf.c.obj
[162/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/netdb.c.obj
[163/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/netifapi.c.obj
[164/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/sockets.c.obj
[165/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/api/tcpip.c.obj
[166/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/apps/sntp/sntp.c.obj
[167/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/apps/netbiosns/netbiosns.c.obj
[168/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/def.c.obj
[169/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/dns.c.obj
[170/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/inet_chksum.c.obj
[171/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/init.c.obj
[172/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ip.c.obj
[173/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/mem.c.obj
[174/1150] Linking C static library esp-idf\esp_http_client\libesp_http_client.a
[175/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/memp.c.obj
[176/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/netif.c.obj
[177/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/pbuf.c.obj
[178/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/raw.c.obj
[179/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/stats.c.obj
[180/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/sys.c.obj
[181/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/tcp.c.obj
[182/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/tcp_in.c.obj
[183/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/tcp_out.c.obj
[184/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/timeouts.c.obj
[185/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/udp.c.obj
[186/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/autoip.c.obj
[187/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/dhcp.c.obj
[188/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/etharp.c.obj
[189/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/icmp.c.obj
[190/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/igmp.c.obj
[191/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/ip4.c.obj
[192/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/ip4_napt.c.obj
[193/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/ip4_addr.c.obj
[194/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv4/ip4_frag.c.obj
[195/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/dhcp6.c.obj
[196/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/ethip6.c.obj
[197/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/icmp6.c.obj
[198/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/inet6.c.obj
[199/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/ip6.c.obj
[200/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/ip6_addr.c.obj
[201/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/ip6_frag.c.obj
[202/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/mld6.c.obj
[203/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/core/ipv6/nd6.c.obj
[204/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ethernet.c.obj
[205/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/bridgeif.c.obj
[206/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/bridgeif_fdb.c.obj
[207/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/slipif.c.obj
[208/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/auth.c.obj
[209/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/ccp.c.obj
[210/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/chap-md5.c.obj
[211/1150] Linking C static library esp-idf\tcp_transport\libtcp_transport.a
[212/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/chap-new.c.obj
[213/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/chap_ms.c.obj
[214/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/demand.c.obj
[215/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/eap.c.obj
[216/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/ecp.c.obj
[217/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/eui64.c.obj
[218/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/fsm.c.obj
[219/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/ipcp.c.obj
[220/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/ipv6cp.c.obj
[221/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/lcp.c.obj
[222/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/magic.c.obj
[223/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/mppe.c.obj
[224/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/multilink.c.obj
[225/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/ppp.c.obj
[226/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/pppapi.c.obj
[227/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/pppcrypt.c.obj
[228/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/pppoe.c.obj
[229/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/pppol2tp.c.obj
[230/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/pppos.c.obj
[231/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/upap.c.obj
[232/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/utils.c.obj
[233/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/vj.c.obj
[234/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/hooks/tcp_isn_default.c.obj
[235/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/hooks/lwip_default_hooks.c.obj
[236/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/debug/lwip_debug.c.obj
[237/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/sockets_ext.c.obj
[238/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/freertos/sys_arch.c.obj
[239/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/acd_dhcp_check.c.obj
[240/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/port/esp32xx/vfs_lwip.c.obj
[241/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/apps/ping/esp_ping.c.obj
[242/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/apps/ping/ping.c.obj
[243/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/apps/ping/ping_sock.c.obj
[244/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/polarssl/arc4.c.obj
[245/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/polarssl/des.c.obj
[246/1150] Linking C static library esp-idf\esp_gdbstub\libesp_gdbstub.a
[247/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/polarssl/md4.c.obj
[248/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/polarssl/md5.c.obj
[249/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/lwip/src/netif/ppp/polarssl/sha1.c.obj
[250/1150] Building C object esp-idf/lwip/CMakeFiles/__idf_lwip.dir/apps/dhcpserver/dhcpserver.c.obj
[251/1150] Building C object esp-idf/vfs/CMakeFiles/__idf_vfs.dir/vfs.c.obj
[252/1150] Building C object esp-idf/vfs/CMakeFiles/__idf_vfs.dir/vfs_eventfd.c.obj
[253/1150] Building C object esp-idf/vfs/CMakeFiles/__idf_vfs.dir/vfs_semihost.c.obj
[254/1150] Building C object esp-idf/vfs/CMakeFiles/__idf_vfs.dir/nullfs.c.obj
[255/1150] Building C object esp-idf/esp_vfs_console/CMakeFiles/__idf_esp_vfs_console.dir/vfs_console.c.obj
[256/1150] Building C object esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/src/phy_override.c.obj
[257/1150] Building C object esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/src/lib_printf.c.obj
[258/1150] Building C object esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/src/phy_common.c.obj
[259/1150] Building C object esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/src/phy_init.c.obj
[260/1150] Building C object esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/esp32s3/phy_init_data.c.obj
[261/1150] Building C object esp-idf/esp_phy/CMakeFiles/__idf_esp_phy.dir/src/btbb_init.c.obj
[262/1150] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/adc_legacy.c.obj
[263/1150] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/adc_dma_legacy.c.obj
[264/1150] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/timer_legacy.c.obj
[265/1150] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/i2c/i2c.c.obj
[266/1150] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/i2s_legacy.c.obj
[267/1150] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/mcpwm_legacy.c.obj
[268/1150] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/pcnt_legacy.c.obj
[269/1150] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/rmt_legacy.c.obj
[270/1150] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/sigma_delta_legacy.c.obj
[271/1150] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/deprecated/rtc_temperature_legacy.c.obj
[272/1150] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/touch_sensor/touch_sensor_common.c.obj
[273/1150] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/touch_sensor/esp32s3/touch_sensor.c.obj
[274/1150] Building C object esp-idf/driver/CMakeFiles/__idf_driver.dir/twai/twai.c.obj
[275/1150] Building C object esp-idf/esp_driver_usb_serial_jtag/CMakeFiles/__idf_esp_driver_usb_serial_jtag.dir/src/usb_serial_jtag.c.obj
[276/1150] Building C object esp-idf/esp_driver_usb_serial_jtag/CMakeFiles/__idf_esp_driver_usb_serial_jtag.dir/src/usb_serial_jtag_connection_monitor.c.obj
[277/1150] Building C object esp-idf/esp_driver_usb_serial_jtag/CMakeFiles/__idf_esp_driver_usb_serial_jtag.dir/src/usb_serial_jtag_vfs.c.obj
[278/1150] Building C object esp-idf/esp_driver_ledc/CMakeFiles/__idf_esp_driver_ledc.dir/src/ledc.c.obj
[279/1150] Building C object esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/i2c_master.c.obj
[280/1150] Building C object esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/i2c_common.c.obj
[281/1150] Building C object esp-idf/esp_driver_i2c/CMakeFiles/__idf_esp_driver_i2c.dir/i2c_slave.c.obj
[282/1150] Building C object esp-idf/esp_driver_sdm/CMakeFiles/__idf_esp_driver_sdm.dir/src/sdm.c.obj
[283/1150] Building C object esp-idf/esp_driver_tsens/CMakeFiles/__idf_esp_driver_tsens.dir/src/temperature_sensor.c.obj
[284/1150] Building C object esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/src/rmt_common.c.obj
[285/1150] Building C object esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/src/rmt_encoder.c.obj
[286/1150] Building C object esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/src/rmt_rx.c.obj
[287/1150] Building C object esp-idf/esp_driver_rmt/CMakeFiles/__idf_esp_driver_rmt.dir/src/rmt_tx.c.obj
[288/1150] Building C object esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/src/sdspi_crc.c.obj
[289/1150] Building C object esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/src/sdspi_host.c.obj
[290/1150] Building C object esp-idf/esp_driver_sdspi/CMakeFiles/__idf_esp_driver_sdspi.dir/src/sdspi_transaction.c.obj
[291/1150] Building C object esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir/src/sdmmc_transaction.c.obj
[292/1150] Linking C static library esp-idf\esp_adc\libesp_adc.a
[293/1150] Building C object esp-idf/esp_driver_sdmmc/CMakeFiles/__idf_esp_driver_sdmmc.dir/src/sdmmc_host.c.obj
[294/1150] Building C object esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_cmd.c.obj
[295/1150] Building C object esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_common.c.obj
[296/1150] Building C object esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_init.c.obj
[297/1150] Building C object esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_io.c.obj
[298/1150] Building C object esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_mmc.c.obj
[299/1150] Building C object esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sdmmc_sd.c.obj
[300/1150] Building C object esp-idf/sdmmc/CMakeFiles/__idf_sdmmc.dir/sd_pwr_ctrl/sd_pwr_ctrl.c.obj
[301/1150] Building C object esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/i2s_common.c.obj
[302/1150] Building C object esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/i2s_std.c.obj
[303/1150] Building C object esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/i2s_pdm.c.obj
[304/1150] Building C object esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/i2s_tdm.c.obj
[305/1150] Building C object esp-idf/esp_driver_i2s/CMakeFiles/__idf_esp_driver_i2s.dir/i2s_platform.c.obj
[306/1150] Building C object esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/src/mcpwm_cap.c.obj
[307/1150] Building C object esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/src/mcpwm_cmpr.c.obj
[308/1150] Building C object esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/src/mcpwm_com.c.obj
[309/1150] Building C object esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/src/mcpwm_fault.c.obj
[310/1150] Building C object esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/src/mcpwm_gen.c.obj
[311/1150] Building C object esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/src/mcpwm_oper.c.obj
[312/1150] Building C object esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/src/mcpwm_sync.c.obj
[313/1150] Building C object esp-idf/esp_driver_mcpwm/CMakeFiles/__idf_esp_driver_mcpwm.dir/src/mcpwm_timer.c.obj
[314/1150] Building C object esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/src/gpspi/spi_common.c.obj
[315/1150] Building C object esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/src/gpspi/spi_master.c.obj
[316/1150] Building C object esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/src/gpspi/spi_slave.c.obj
[317/1150] Building C object esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/src/gpspi/spi_dma.c.obj
[318/1150] Building C object esp-idf/esp_driver_spi/CMakeFiles/__idf_esp_driver_spi.dir/src/gpspi/spi_slave_hd.c.obj
[319/1150] Building C object esp-idf/esp_driver_pcnt/CMakeFiles/__idf_esp_driver_pcnt.dir/src/pulse_cnt.c.obj
[320/1150] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_api.cpp.obj
[321/1150] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_cxx_api.cpp.obj
[322/1150] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_item_hash_list.cpp.obj
[323/1150] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_page.cpp.obj
[324/1150] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_pagemanager.cpp.obj
[325/1150] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_storage.cpp.obj
[326/1150] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_handle_simple.cpp.obj
[327/1150] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_handle_locked.cpp.obj
[328/1150] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_partition.cpp.obj
[329/1150] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_partition_lookup.cpp.obj
[330/1150] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_partition_manager.cpp.obj
[331/1150] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_types.cpp.obj
[332/1150] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_platform.cpp.obj
[333/1150] Building C object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_bootloader.c.obj
[334/1150] Linking C static library esp-idf\esp-tls\libesp-tls.a
[335/1150] Building CXX object esp-idf/nvs_flash/CMakeFiles/__idf_nvs_flash.dir/src/nvs_encrypted_partition.cpp.obj
[336/1150] Building C object esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/default_event_loop.c.obj
[337/1150] Building C object esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/esp_event.c.obj
[338/1150] Building C object esp-idf/esp_event/CMakeFiles/__idf_esp_event.dir/esp_event_private.c.obj
[339/1150] Building C object esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/src/uart.c.obj
[340/1150] Building C object esp-idf/esp_driver_uart/CMakeFiles/__idf_esp_driver_uart.dir/src/uart_vfs.c.obj
[341/1150] Building C object esp-idf/esp_ringbuf/CMakeFiles/__idf_esp_ringbuf.dir/ringbuf.c.obj
[342/1150] Building C object esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/src/gptimer.c.obj
[343/1150] Building C object esp-idf/esp_driver_gptimer/CMakeFiles/__idf_esp_driver_gptimer.dir/src/gptimer_common.c.obj
[344/1150] Building C object esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/esp_timer.c.obj
[345/1150] Building C object esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/esp_timer_init.c.obj
[346/1150] Building C object esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/ets_timer_legacy.c.obj
[347/1150] Building C object esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/system_time.c.obj
[348/1150] Building C object esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/esp_timer_impl_common.c.obj
[349/1150] Building C object esp-idf/esp_timer/CMakeFiles/__idf_esp_timer.dir/src/esp_timer_impl_systimer.c.obj
[350/1150] Building CXX object esp-idf/cxx/CMakeFiles/__idf_cxx.dir/cxx_exception_stubs.cpp.obj
[351/1150] Building CXX object esp-idf/cxx/CMakeFiles/__idf_cxx.dir/cxx_guards.cpp.obj
[352/1150] Building CXX object esp-idf/cxx/CMakeFiles/__idf_cxx.dir/cxx_init.cpp.obj
[353/1150] Building C object esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread.c.obj
[354/1150] Building C object esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread_cond_var.c.obj
[355/1150] Building C object esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread_local_storage.c.obj
[356/1150] Building C object esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread_rwlock.c.obj
[357/1150] Building C object esp-idf/pthread/CMakeFiles/__idf_pthread.dir/pthread_semaphore.c.obj
[358/1150] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/abort.c.obj
[359/1150] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/assert.c.obj
[360/1150] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/heap.c.obj
[361/1150] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/flockfile.c.obj
[362/1150] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/locks.c.obj
[363/1150] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/poll.c.obj
[364/1150] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/pthread.c.obj
[365/1150] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/random.c.obj
[366/1150] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/getentropy.c.obj
[367/1150] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/reent_init.c.obj
[368/1150] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/newlib_init.c.obj
[369/1150] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/syscalls.c.obj
[370/1150] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/termios.c.obj
[371/1150] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/stdatomic.c.obj
[372/1150] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/time.c.obj
[373/1150] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/sysconf.c.obj
[374/1150] Linking C static library esp-idf\http_parser\libhttp_parser.a
[375/1150] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/realpath.c.obj
[376/1150] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/scandir.c.obj
[377/1150] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/port/xtensa/stdatomic_s32c1i.c.obj
[378/1150] Building C object esp-idf/newlib/CMakeFiles/__idf_newlib.dir/port/esp_time_impl.c.obj
[379/1150] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/heap_idf.c.obj
[380/1150] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/app_startup.c.obj
[381/1150] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/port_common.c.obj
[382/1150] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/port_systick.c.obj
[383/1150] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/list.c.obj
[384/1150] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/queue.c.obj
[385/1150] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/tasks.c.obj
[386/1150] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/timers.c.obj
[387/1150] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/event_groups.c.obj
[388/1150] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/stream_buffer.c.obj
[389/1150] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/portable/xtensa/port.c.obj
[390/1150] Building ASM object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/portable/xtensa/portasm.S.obj
[391/1150] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/portable/xtensa/xtensa_init.c.obj
[392/1150] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/FreeRTOS-Kernel/portable/xtensa/xtensa_overlay_os_hook.c.obj
[393/1150] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/esp_additions/freertos_compatibility.c.obj
[394/1150] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/esp_additions/idf_additions_event_groups.c.obj
[395/1150] Building C object esp-idf/freertos/CMakeFiles/__idf_freertos.dir/esp_additions/idf_additions.c.obj
[396/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj
[397/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj
[398/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj
[399/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj
[400/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_clk.c.obj
[401/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/clk_ctrl_os.c.obj
[402/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/hw_random.c.obj
[403/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/intr_alloc.c.obj
[404/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/mac_addr.c.obj
[405/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/periph_ctrl.c.obj
[406/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/revision.c.obj
[407/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/rtc_module.c.obj
[408/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_modem.c.obj
[409/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_modes.c.obj
[410/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_console.c.obj
[411/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_usb.c.obj
[412/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_gpio.c.obj
[413/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_event.c.obj
[414/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/regi2c_ctrl.c.obj
[415/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sar_periph_ctrl_common.c.obj
[416/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/io_mux.c.obj
[417/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_clk_tree.c.obj
[418/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp_clk_tree_common.c.obj
[419/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/esp_dma_utils.c.obj
[420/1150] Linking C static library esp-idf\esp_wifi\libesp_wifi.a
[421/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/gdma_link.c.obj
[422/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/spi_share_hw_ctrl.c.obj
[423/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/spi_bus_lock.c.obj
[424/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/clk_utils.c.obj
[425/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/adc_share_hw_ctrl.c.obj
[426/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/gdma.c.obj
[427/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/deprecated/gdma_legacy.c.obj
[428/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/esp_async_memcpy.c.obj
[429/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/dma/async_memcpy_gdma.c.obj
[430/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/mspi_timing_tuning.c.obj
[431/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/mspi_timing_by_mspi_delay.c.obj
[432/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/sleep_wake_stub.c.obj
[433/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_clock_output.c.obj
[434/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj
[435/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj
[436/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj
[437/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj
[438/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj
[439/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj
[440/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/sar_periph_ctrl.c.obj
[441/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/mspi_timing_config.c.obj
[442/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_memprot.c.obj
[443/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp_memprot_conv.c.obj
[444/1150] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/lowpower/port/esp32s3/sleep_cpu.c.obj
[445/1150] Building C object esp-idf/esp_security/CMakeFiles/__idf_esp_security.dir/src/init.c.obj
[446/1150] Building C object esp-idf/esp_security/CMakeFiles/__idf_esp_security.dir/src/esp_hmac.c.obj
[447/1150] Building C object esp-idf/esp_security/CMakeFiles/__idf_esp_security.dir/src/esp_ds.c.obj
[448/1150] Building C object esp-idf/esp_security/CMakeFiles/__idf_esp_security.dir/src/esp_crypto_lock.c.obj
[449/1150] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj
[450/1150] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj
[451/1150] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj
[452/1150] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj
[453/1150] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj
[454/1150] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj
[455/1150] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj
[456/1150] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj
[457/1150] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj
[458/1150] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj
[459/1150] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj
[460/1150] Linking C static library esp-idf\esp_coex\libesp_coex.a
[461/1150] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj
[462/1150] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj
[463/1150] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj
[464/1150] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj
[465/1150] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/wdt_periph.c.obj
[466/1150] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj
[467/1150] Building C object esp-idf/heap/CMakeFiles/__idf_heap.dir/heap_caps_base.c.obj
[468/1150] Building C object esp-idf/heap/CMakeFiles/__idf_heap.dir/heap_caps.c.obj
[469/1150] Building C object esp-idf/heap/CMakeFiles/__idf_heap.dir/heap_caps_init.c.obj
[470/1150] Building C object esp-idf/heap/CMakeFiles/__idf_heap.dir/multi_heap.c.obj
[471/1150] Building C object esp-idf/heap/CMakeFiles/__idf_heap.dir/tlsf/tlsf.c.obj
[472/1150] Building C object esp-idf/heap/CMakeFiles/__idf_heap.dir/port/memory_layout_utils.c.obj
[473/1150] Building C object esp-idf/heap/CMakeFiles/__idf_heap.dir/port/esp32s3/memory_layout.c.obj
[474/1150] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/os/log_timestamp.c.obj
[475/1150] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj
[476/1150] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/os/log_lock.c.obj
[477/1150] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/os/log_write.c.obj
[478/1150] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/buffer/log_buffers.c.obj
[479/1150] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/log_level/log_level.c.obj
[480/1150] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/log_level/tag_log_level/tag_log_level.c.obj
[481/1150] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/log_level/tag_log_level/linked_list/log_linked_list.c.obj
[482/1150] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/log_level/tag_log_level/cache/log_binary_heap.c.obj
[483/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj
[484/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj
[485/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj
[486/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj
[487/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj
[488/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj
[489/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/color_hal.c.obj
[490/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_flash_hal.c.obj
[491/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_flash_hal_iram.c.obj
[492/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_flash_encrypt_hal_iram.c.obj
[493/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/clk_tree_hal.c.obj
[494/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/systimer_hal.c.obj
[495/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/uart_hal.c.obj
[496/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/uart_hal_iram.c.obj
[497/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/gpio_hal.c.obj
[498/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/rtc_io_hal.c.obj
[499/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/timer_hal.c.obj
[500/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/ledc_hal.c.obj
[501/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/ledc_hal_iram.c.obj
[502/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/i2c_hal.c.obj
[503/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/i2c_hal_iram.c.obj
[504/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/rmt_hal.c.obj
[505/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/pcnt_hal.c.obj
[506/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/mcpwm_hal.c.obj
[507/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/twai_hal.c.obj
[508/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/twai_hal_iram.c.obj
[509/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/gdma_hal_top.c.obj
[510/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/gdma_hal_ahb_v1.c.obj
[511/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/i2s_hal.c.obj
[512/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/sdm_hal.c.obj
[513/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/sdmmc_hal.c.obj
[514/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/adc_hal_common.c.obj
[515/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/adc_oneshot_hal.c.obj
[516/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/adc_hal.c.obj
[517/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/lcd_hal.c.obj
[518/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/mpi_hal.c.obj
[519/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/sha_hal.c.obj
[520/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/aes_hal.c.obj
[521/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/brownout_hal.c.obj
[522/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_hal.c.obj
[523/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_hal_iram.c.obj
[524/1150] Linking C static library esp-idf\wpa_supplicant\libwpa_supplicant.a
[525/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_slave_hal.c.obj
[526/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_slave_hal_iram.c.obj
[527/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_slave_hd_hal.c.obj
[528/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/spi_flash_hal_gpspi.c.obj
[529/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/hmac_hal.c.obj
[530/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/ds_hal.c.obj
[531/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/usb_serial_jtag_hal.c.obj
[532/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/usb_dwc_hal.c.obj
[533/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/usb_wrap_hal.c.obj
[534/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/touch_sensor_hal.c.obj
[535/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/touch_sensor_hal.c.obj
[536/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/xt_wdt_hal.c.obj
[537/1150] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/rtc_cntl_hal.c.obj
[538/1150] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj
[539/1150] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj
[540/1150] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj
[541/1150] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj
[542/1150] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj
[543/1150] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj
[544/1150] Building ASM object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj
[545/1150] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj
[546/1150] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj
[547/1150] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj
[548/1150] Building ASM object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj
[549/1150] Building C object esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj
[550/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj
[551/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/crosscore_int.c.obj
[552/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_ipc.c.obj
[553/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/freertos_hooks.c.obj
[554/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/int_wdt.c.obj
[555/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/panic.c.obj
[556/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_system.c.obj
[557/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/startup.c.obj
[558/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/startup_funcs.c.obj
[559/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/system_time.c.obj
[560/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/stack_check.c.obj
[561/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/ubsan.c.obj
[562/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/task_wdt/task_wdt.c.obj
[563/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/task_wdt/task_wdt_impl_timergroup.c.obj
[564/1150] Linking C static library esp-idf\esp_netif\libesp_netif.a
[565/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/cpu_start.c.obj
[566/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/panic_handler.c.obj
[567/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/esp_system_chip.c.obj
[568/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/image_process.c.obj
[569/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/brownout.c.obj
[570/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/esp_ipc_isr.c.obj
[571/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/esp_ipc_isr_port.c.obj
[572/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/xt_wdt.c.obj
[573/1150] Building ASM object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/esp_ipc_isr_handler.S.obj
[574/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/panic_arch.c.obj
[575/1150] Building ASM object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/panic_handler_asm.S.obj
[576/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/expression_with_stack.c.obj
[577/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/debug_helpers.c.obj
[578/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/debug_stubs.c.obj
[579/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/arch/xtensa/trax.c.obj
[580/1150] Building ASM object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32s3/highint_hdl.S.obj
[581/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32s3/clk.c.obj
[582/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32s3/reset_reason.c.obj
[583/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32s3/system_internal.c.obj
[584/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32s3/cache_err_int.c.obj
[585/1150] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/port/soc/esp32s3/apb_backup_dma.c.obj
[586/1150] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/flash_brownout_hook.c.obj
[587/1150] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/esp32s3/spi_flash_oct_flash_init.c.obj
[588/1150] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_hpm_enable.c.obj
[589/1150] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_drivers.c.obj
[590/1150] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_generic.c.obj
[591/1150] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_issi.c.obj
[592/1150] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_mxic.c.obj
[593/1150] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_gd.c.obj
[594/1150] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_winbond.c.obj
[595/1150] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_boya.c.obj
[596/1150] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_mxic_opi.c.obj
[597/1150] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_chip_th.c.obj
[598/1150] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/memspi_host_driver.c.obj
[599/1150] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/cache_utils.c.obj
[600/1150] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/flash_mmap.c.obj
[601/1150] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/flash_ops.c.obj
[602/1150] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj
[603/1150] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/esp_flash_api.c.obj
[604/1150] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/esp_flash_spi_init.c.obj
[605/1150] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_os_func_app.c.obj
[606/1150] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_os_func_noos.c.obj
[607/1150] Building C object esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/esp_mmu_map.c.obj
[608/1150] Building C object esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/port/esp32s3/ext_mem_layout.c.obj
[609/1150] Building C object esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/esp_cache.c.obj
[610/1150] Building C object esp-idf/esp_mm/CMakeFiles/__idf_esp_mm.dir/heap_align_hw.c.obj
[611/1150] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj
[612/1150] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj
[613/1150] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj
[614/1150] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj
[615/1150] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj
[616/1150] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj
[617/1150] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj
[618/1150] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj
[619/1150] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj
[620/1150] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj
[621/1150] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj
[622/1150] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj
[623/1150] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj
[624/1150] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj
[625/1150] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj
[626/1150] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/idf/bootloader_sha.c.obj
[627/1150] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/secure_boot_secure_features.c.obj
[628/1150] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj
[629/1150] Linking C static library esp-idf\lwip\liblwip.a
[630/1150] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj
[631/1150] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj
[632/1150] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj
[633/1150] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj
[634/1150] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj
[635/1150] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj
[636/1150] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj
[637/1150] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_startup.c.obj
[638/1150] Building C object esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/partition.c.obj
[639/1150] Building C object esp-idf/esp_partition/CMakeFiles/__idf_esp_partition.dir/partition_target.c.obj
[640/1150] Building C object esp-idf/app_update/CMakeFiles/__idf_app_update.dir/esp_ota_ops.c.obj
[641/1150] Building C object esp-idf/app_update/CMakeFiles/__idf_app_update.dir/esp_ota_app_desc.c.obj
[642/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/debug.c.obj
[643/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/mps_reader.c.obj
[644/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/mps_trace.c.obj
[645/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_cache.c.obj
[646/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_ciphersuites.c.obj
[647/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_client.c.obj
[648/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_cookie.c.obj
[649/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_debug_helpers_generated.c.obj
[650/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_msg.c.obj
[651/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_ticket.c.obj
[652/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls.c.obj
[653/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls12_client.c.obj
[654/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls12_server.c.obj
[655/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls13_keys.c.obj
[656/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls13_server.c.obj
[657/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls13_client.c.obj
[658/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/ssl_tls13_generic.c.obj
[659/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/mbedtls_debug.c.obj
[660/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/esp_platform_time.c.obj
[661/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedtls.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/net_sockets.c.obj
[662/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/pkcs7.c.obj
[663/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509.c.obj
[664/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509_create.c.obj
[665/1150] Linking C static library esp-idf\vfs\libvfs.a
[666/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509_crl.c.obj
[667/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509_crt.c.obj
[668/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509_csr.c.obj
[669/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509write.c.obj
[670/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509write_crt.c.obj
[671/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedx509.dir/x509write_csr.c.obj
[672/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/aes.c.obj
[673/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/aesni.c.obj
[674/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/aesce.c.obj
[675/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/aria.c.obj
[676/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/asn1parse.c.obj
[677/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/asn1write.c.obj
[678/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/base64.c.obj
[679/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/bignum.c.obj
[680/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/bignum_core.c.obj
[681/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/bignum_mod.c.obj
[682/1150] Building C object esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj
[683/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/bignum_mod_raw.c.obj
[684/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/block_cipher.c.obj
[685/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/camellia.c.obj
[686/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ccm.c.obj
[687/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/chacha20.c.obj
[688/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/chachapoly.c.obj
[689/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/cipher.c.obj
[690/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/cipher_wrap.c.obj
[691/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/constant_time.c.obj
[692/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/cmac.c.obj
[693/1150] Building C object esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/esp_app_desc.c.obj
[694/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ctr_drbg.c.obj
[695/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/des.c.obj
[696/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/dhm.c.obj
[697/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecdh.c.obj
[698/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecdsa.c.obj
[699/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecjpake.c.obj
[700/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecp.c.obj
[701/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecp_curves.c.obj
[702/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ecp_curves_new.c.obj
[703/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/entropy.c.obj
[704/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/entropy_poll.c.obj
[705/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/error.c.obj
[706/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/gcm.c.obj
[707/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/hkdf.c.obj
[708/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/hmac_drbg.c.obj
[709/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/lmots.c.obj
[710/1150] Linking C static library esp-idf\esp_vfs_console\libesp_vfs_console.a
[711/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/lms.c.obj
[712/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/md.c.obj
[713/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/md5.c.obj
[714/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/memory_buffer_alloc.c.obj
[715/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/nist_kw.c.obj
[716/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/oid.c.obj
[717/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/padlock.c.obj
[718/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pem.c.obj
[719/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pk.c.obj
[720/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pk_ecc.c.obj
[721/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pk_wrap.c.obj
[722/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pkcs12.c.obj
[723/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pkcs5.c.obj
[724/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pkparse.c.obj
[725/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/pkwrite.c.obj
[726/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/platform.c.obj
[727/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/platform_util.c.obj
[728/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/poly1305.c.obj
[729/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto.c.obj
[730/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_aead.c.obj
[731/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_cipher.c.obj
[732/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_client.c.obj
[733/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_driver_wrappers_no_static.c.obj
[734/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_ecp.c.obj
[735/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_ffdh.c.obj
[736/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_hash.c.obj
[737/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_mac.c.obj
[738/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_pake.c.obj
[739/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_rsa.c.obj
[740/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_se.c.obj
[741/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_slot_management.c.obj
[742/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_crypto_storage.c.obj
[743/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_its_file.c.obj
[744/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/psa_util.c.obj
[745/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/ripemd160.c.obj
[746/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/rsa.c.obj
[747/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/rsa_alt_helpers.c.obj
[748/1150] Linking C static library esp-idf\esp_phy\libesp_phy.a
[749/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/sha1.c.obj
[750/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/sha256.c.obj
[751/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/sha512.c.obj
[752/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/sha3.c.obj
[753/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/threading.c.obj
[754/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/timing.c.obj
[755/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/version.c.obj
[756/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/version_features.c.obj
[757/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/sha/dma/esp_sha_gdma_impl.c.obj
[758/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/aes/dma/esp_aes_gdma_impl.c.obj
[759/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/aes/dma/esp_aes_dma_core.c.obj
[760/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/crypto_shared_gdma/esp_crypto_shared_gdma.c.obj
[761/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/esp_hardware.c.obj
[762/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/esp_mem.c.obj
[763/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/esp_timing.c.obj
[764/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/aes/esp_aes_xts.c.obj
[765/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/aes/esp_aes_common.c.obj
[766/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/aes/dma/esp_aes.c.obj
[767/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/sha/esp_sha.c.obj
[768/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/sha/dma/sha.c.obj
[769/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/esp_ds/esp_rsa_sign_alt.c.obj
[770/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/bignum/esp_bignum.c.obj
[771/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/bignum/bignum_alt.c.obj
[772/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/sha/dma/esp_sha1.c.obj
[773/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/sha/dma/esp_sha256.c.obj
[774/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/sha/dma/esp_sha512.c.obj
[775/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/aes/esp_aes_gcm.c.obj
[776/1150] Building C object esp-idf/mbedtls/mbedtls/library/CMakeFiles/mbedcrypto.dir/C_/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/md/esp_md.c.obj
[777/1150] Building C object esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/p256-m_driver_entrypoints.c.obj
[778/1150] Building C object esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/library/everest.c.obj
[779/1150] Building C object esp-idf/mbedtls/mbedtls/3rdparty/p256-m/CMakeFiles/p256m.dir/p256-m/p256-m.c.obj
[780/1150] Building C object esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/library/x25519.c.obj
[781/1150] Building C object esp-idf/mbedtls/mbedtls/3rdparty/everest/CMakeFiles/everest.dir/library/Hacl_Curve25519_joined.c.obj
[782/1150] Linking C static library esp-idf\driver\libdriver.a
[783/1150] Linking C static library esp-idf\esp_driver_usb_serial_jtag\libesp_driver_usb_serial_jtag.a
[784/1150] Linking C static library esp-idf\esp_driver_ledc\libesp_driver_ledc.a
[785/1150] Linking C static library esp-idf\esp_driver_i2c\libesp_driver_i2c.a
[786/1150] Linking C static library esp-idf\esp_driver_sdm\libesp_driver_sdm.a
[787/1150] Linking C static library esp-idf\esp_driver_tsens\libesp_driver_tsens.a
[788/1150] Linking C static library esp-idf\esp_driver_rmt\libesp_driver_rmt.a
[789/1150] Linking C static library esp-idf\esp_driver_sdspi\libesp_driver_sdspi.a
[790/1150] Linking C static library esp-idf\esp_driver_sdmmc\libesp_driver_sdmmc.a
[791/1150] Linking C static library esp-idf\sdmmc\libsdmmc.a
[792/1150] Linking C static library esp-idf\esp_driver_i2s\libesp_driver_i2s.a
[793/1150] Linking C static library esp-idf\esp_driver_mcpwm\libesp_driver_mcpwm.a
[794/1150] Linking C static library esp-idf\esp_driver_spi\libesp_driver_spi.a
[795/1150] Linking C static library esp-idf\esp_driver_pcnt\libesp_driver_pcnt.a
[796/1150] Linking C static library esp-idf\nvs_flash\libnvs_flash.a
[797/1150] Linking C static library esp-idf\esp_event\libesp_event.a
[798/1150] Linking C static library esp-idf\esp_driver_uart\libesp_driver_uart.a
[799/1150] Linking C static library esp-idf\esp_ringbuf\libesp_ringbuf.a
[800/1150] Linking C static library esp-idf\esp_driver_gptimer\libesp_driver_gptimer.a
[801/1150] Linking C static library esp-idf\esp_timer\libesp_timer.a
[802/1150] Linking C static library esp-idf\cxx\libcxx.a
[803/1150] Linking C static library esp-idf\pthread\libpthread.a
[804/1150] Linking C static library esp-idf\newlib\libnewlib.a
[805/1150] Linking C static library esp-idf\freertos\libfreertos.a
[806/1150] Linking C static library esp-idf\esp_hw_support\libesp_hw_support.a
[807/1150] Linking C static library esp-idf\esp_security\libesp_security.a
[808/1150] Linking C static library esp-idf\soc\libsoc.a
[809/1150] Linking C static library esp-idf\heap\libheap.a
[810/1150] Linking C static library esp-idf\log\liblog.a
[811/1150] Linking C static library esp-idf\hal\libhal.a
[812/1150] Linking C static library esp-idf\esp_rom\libesp_rom.a
[813/1150] Linking C static library esp-idf\esp_common\libesp_common.a
[814/1150] Linking C static library esp-idf\esp_system\libesp_system.a
[815/1150] Linking C static library esp-idf\spi_flash\libspi_flash.a
[816/1150] Linking C static library esp-idf\esp_mm\libesp_mm.a
[817/1150] Linking C static library esp-idf\bootloader_support\libbootloader_support.a
[818/1150] Linking C static library esp-idf\efuse\libefuse.a
[819/1150] Linking C static library esp-idf\esp_partition\libesp_partition.a
[820/1150] Linking C static library esp-idf\app_update\libapp_update.a
[821/1150] Linking C static library esp-idf\esp_bootloader_format\libesp_bootloader_format.a
[822/1150] Linking C static library esp-idf\esp_app_format\libesp_app_format.a
[823/1150] Linking CXX static library esp-idf\mbedtls\mbedtls\library\libmbedtls.a
[824/1150] Linking CXX static library esp-idf\mbedtls\mbedtls\library\libmbedx509.a
[825/1150] Performing build step for 'bootloader'
[0/1] Re-running CMake...
-- Building ESP-IDF components for target esp32s3
-- Project sdkconfig file D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/sdkconfig
-- Compiler supported targets: xtensa-esp-elf
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- Bootloader project name: "bootloader" version: 1
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.bt_funcs.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader/subproject/main/ld/esp32s3/bootloader.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader/subproject/main/ld/esp32s3/bootloader.rom.ld
-- Components: bootloader bootloader_support efuse esp_app_format esp_bootloader_format esp_common esp_hw_support esp_rom esp_security esp_system esptool_py freertos hal log main micro-ecc newlib partition_table soc spi_flash xtensa
-- Component paths: C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support C:/Users/<USER>/esp/v5.4.1/esp-idf/components/efuse C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_app_format C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_bootloader_format C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_common C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_security C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/bootloader_components/main C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader/subproject/components/micro-ecc C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash C:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa
-- Configuring done (29.4s)
-- Generating done (0.4s)
-- Build files have been written to: D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader
[1/101] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_lock.c.obj
[2/101] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_timestamp.c.obj
[3/101] Building C object esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj
[4/101] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj
[5/101] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj
[6/101] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj
[7/101] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj
[8/101] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj
[9/101] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj
[10/101] Building ASM object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj
[11/101] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj
[12/101] Building ASM object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj
[13/101] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj
[14/101] Building C object esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj
[15/101] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj
[16/101] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj
[17/101] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj
[18/101] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj
[19/101] Building C object esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj
[20/101] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj
[21/101] Linking C static library esp-idf\log\liblog.a
[22/101] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj
[23/101] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj
[24/101] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj
[25/101] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj
[26/101] Building C object esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj
[27/101] Building C object esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj
[28/101] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj
[29/101] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj
[30/101] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj
[31/101] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj
[32/101] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj
[33/101] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj
[34/101] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj
[35/101] Building C object esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj
[36/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj
[37/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj
[38/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj
[39/101] Linking C static library esp-idf\esp_rom\libesp_rom.a
[40/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj
[41/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj
[42/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj
[43/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj
[44/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj
[45/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj
[46/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj
[47/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj
[48/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj
[49/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj
[50/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj
[51/101] Linking C static library esp-idf\esp_common\libesp_common.a
[52/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj
[53/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj
[54/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj
[55/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj
[56/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj
[57/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj
[58/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj
[59/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj
[60/101] Building C object esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj
[61/101] Building C object esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj
[62/101] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj
[63/101] Building C object esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj
[64/101] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj
[65/101] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj
[66/101] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj
[67/101] Linking C static library esp-idf\esp_hw_support\libesp_hw_support.a
[68/101] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj
[69/101] Building C object esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj
[70/101] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj
[71/101] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj
[72/101] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj
[73/101] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj
[74/101] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj
[75/101] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj
[76/101] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj
[77/101] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj
[78/101] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj
[79/101] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj
[80/101] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj
[81/101] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj
[82/101] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj
[83/101] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj
[84/101] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj
[85/101] Linking C static library esp-idf\esp_system\libesp_system.a
[86/101] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/wdt_periph.c.obj
[87/101] Building C object esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj
[88/101] Building C object esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj
[89/101] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj
[90/101] Linking C static library esp-idf\efuse\libefuse.a
[91/101] Linking C static library esp-idf\bootloader_support\libbootloader_support.a
[92/101] Linking C static library esp-idf\esp_bootloader_format\libesp_bootloader_format.a
[93/101] Linking C static library esp-idf\spi_flash\libspi_flash.a
[94/101] Linking C static library esp-idf\hal\libhal.a
[95/101] Linking C static library esp-idf\soc\libsoc.a
[96/101] Linking C static library esp-idf\xtensa\libxtensa.a
[97/101] Linking C static library esp-idf\main\libmain.a
[98/101] Linking C executable bootloader.elf
[99/101] Generating binary image from built executable
esptool.py v4.8.1

Creating esp32s3 image...

Merged 2 ELF sections

Successfully created esp32s3 image.

Generated D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/bootloader.bin
[100/101] C:\Windows\system32\cmd.exe /C "cd /D D:\BLE\Bitbucket\Feature_ble_fw_esp32\APP_ESP32\build\bootloader\esp-idf\esptool_py && C:\Users\<USER>\.espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/bootloader.bin"

Bootloader binary size 0x51e0 bytes. 0x2e20 bytes (36%) free.


[826/1150] No install step for 'bootloader'
[827/1150] Linking CXX static library esp-idf\mbedtls\mbedtls\library\libmbedcrypto.a
[828/1150] Completed 'bootloader'
[829/1150] Linking CXX static library esp-idf\mbedtls\mbedtls\3rdparty\p256-m\libp256m.a
[830/1150] Linking CXX static library esp-idf\mbedtls\mbedtls\3rdparty\everest\libeverest.a
[831/1150] Building C object esp-idf/mbedtls/CMakeFiles/__idf_mbedtls.dir/esp_crt_bundle/esp_crt_bundle.c.obj
[832/1150] Building C object esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/pm_locks.c.obj
[833/1150] Building C object esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/pm_trace.c.obj
[834/1150] Building C object esp-idf/esp_pm/CMakeFiles/__idf_esp_pm.dir/pm_impl.c.obj
[835/1150] Building C object esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/src/gpio.c.obj
[836/1150] Building C object esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/src/gpio_glitch_filter_ops.c.obj
[837/1150] Building C object esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/src/rtc_io.c.obj
[838/1150] Building C object esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/src/dedic_gpio.c.obj
[839/1150] Building C object esp-idf/esp_driver_gpio/CMakeFiles/__idf_esp_driver_gpio.dir/src/gpio_pin_glitch_filter.c.obj
[840/1150] Building C object esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj
[841/1150] Building ASM object esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xtensa_context.S.obj
[842/1150] Building ASM object esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xtensa_intr_asm.S.obj
[843/1150] Building C object esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xtensa_intr.c.obj
[844/1150] Building ASM object esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xtensa_vectors.S.obj
[845/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/controller/esp32c3/bt.c.obj
[846/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/btc/core/btc_alarm.c.obj
[847/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/api/esp_blufi_api.c.obj
[848/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/hci_log/bt_hci_log.c.obj
[849/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/btc/core/btc_manage.c.obj
[850/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/btc/core/btc_task.c.obj
[851/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/btc/profile/esp/blufi/blufi_prf.c.obj
[852/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/btc/profile/esp/blufi/blufi_protocol.c.obj
[853/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/osi/alarm.c.obj
[854/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/osi/allocator.c.obj
[855/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/osi/buffer.c.obj
[856/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/osi/config.c.obj
[857/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/osi/fixed_queue.c.obj
[858/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/osi/pkt_queue.c.obj
[859/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/osi/fixed_pkt_queue.c.obj
[860/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/osi/future.c.obj
[861/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/osi/hash_functions.c.obj
[862/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/osi/hash_map.c.obj
[863/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/osi/list.c.obj
[864/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/osi/mutex.c.obj
[865/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/osi/thread.c.obj
[866/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/osi/osi.c.obj
[867/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/osi/semaphore.c.obj
[868/1150] Linking C static library esp-idf\mbedtls\libmbedtls.a
[869/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/porting/mem/bt_osi_mem.c.obj
[870/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/ble_log/ble_log_spi_out.c.obj
[871/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/transport/src/transport.c.obj
[872/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/util/src/addr.c.obj
[873/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/services/gatt/src/ble_svc_gatt.c.obj
[874/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/services/tps/src/ble_svc_tps.c.obj
[875/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/services/ias/src/ble_svc_ias.c.obj
[876/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/services/ipss/src/ble_svc_ipss.c.obj
[877/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/services/ans/src/ble_svc_ans.c.obj
[878/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/services/hr/src/ble_svc_hr.c.obj
[879/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/services/htp/src/ble_svc_htp.c.obj
[880/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/services/gap/src/ble_svc_gap.c.obj
[881/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/services/bas/src/ble_svc_bas.c.obj
[882/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/services/dis/src/ble_svc_dis.c.obj
[883/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/services/lls/src/ble_svc_lls.c.obj
[884/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/services/prox/src/ble_svc_prox.c.obj
[885/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/services/cts/src/ble_svc_cts.c.obj
[886/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/services/hid/src/ble_svc_hid.c.obj
[887/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/services/sps/src/ble_svc_sps.c.obj
[888/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/services/cte/src/ble_svc_cte.c.obj
[889/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_hs_conn.c.obj
[890/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_store_util.c.obj
[891/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_sm.c.obj
[892/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_l2cap_sig_cmd.c.obj
[893/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_hs_hci_cmd.c.obj
[894/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_hs_id.c.obj
[895/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_att_svr.c.obj
[896/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_gatts_lcl.c.obj
[897/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_ibeacon.c.obj
[898/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_hs_atomic.c.obj
[899/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_sm_alg.c.obj
[900/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_hs_stop.c.obj
[901/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_hs.c.obj
[902/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_hs_hci_evt.c.obj
[903/1150] Linking C static library esp-idf\esp_pm\libesp_pm.a
[904/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_hs_mqueue.c.obj
[905/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_hs_periodic_sync.c.obj
[906/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_att.c.obj
[907/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_ead.c.obj
[908/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_aes_ccm.c.obj
[909/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_gattc.c.obj
[910/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_store.c.obj
[911/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_sm_lgcy.c.obj
[912/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_hs_cfg.c.obj
[913/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_att_clt.c.obj
[914/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_l2cap_coc.c.obj
[915/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_hs_mbuf.c.obj
[916/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_att_cmd.c.obj
[917/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_hs_log.c.obj
[918/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_eddystone.c.obj
[919/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_hs_startup.c.obj
[920/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_l2cap_sig.c.obj
[921/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_gap.c.obj
[922/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_sm_cmd.c.obj
[923/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_uuid.c.obj
[924/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_hs_pvcy.c.obj
[925/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_hs_flow.c.obj
[926/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_l2cap.c.obj
[927/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_sm_sc.c.obj
[928/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_hs_misc.c.obj
[929/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_gatts.c.obj
[930/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_hs_adv.c.obj
[931/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_hs_hci.c.obj
[932/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_hs_hci_util.c.obj
[933/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_hs_resolv.c.obj
[934/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/store/ram/src/ble_store_ram.c.obj
[935/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/store/config/src/ble_store_config.c.obj
[936/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/store/config/src/ble_store_nvs.c.obj
[937/1150] Linking C static library esp-idf\esp_driver_gpio\libesp_driver_gpio.a
[938/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_gattc_cache.c.obj
[939/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_gattc_cache_conn.c.obj
[940/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/host/src/ble_eatt.c.obj
[941/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/porting/nimble/src/nimble_port.c.obj
[942/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/porting/npl/freertos/src/nimble_port_freertos.c.obj
[943/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/port/src/nvs_port.c.obj
[944/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/porting/nimble/src/os_mempool.c.obj
[945/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/porting/nimble/src/mem.c.obj
[946/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/porting/nimble/src/os_mbuf.c.obj
[947/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/porting/nimble/src/os_msys_init.c.obj
[948/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/porting/npl/freertos/src/npl_os_freertos.c.obj
[949/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/esp-hci/src/esp_nimble_hci.c.obj
[950/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/host/nimble/nimble/nimble/transport/esp_ipc_legacy/src/hci_esp_ipc_legacy.c.obj
[951/1150] Building C object esp-idf/bt/CMakeFiles/__idf_bt.dir/common/btc/profile/esp/blufi/nimble_host/esp_blufi.c.obj
[952/1150] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/commands.c.obj
[953/1150] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/esp_console_common.c.obj
[954/1150] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/split_argv.c.obj
[955/1150] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/linenoise/linenoise.c.obj
[956/1150] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/esp_console_repl_chip.c.obj
[957/1150] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_cmd.c.obj
[958/1150] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_date.c.obj
[959/1150] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_dbl.c.obj
[960/1150] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_dstr.c.obj
[961/1150] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_end.c.obj
[962/1150] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_file.c.obj
[963/1150] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_hashtable.c.obj
[964/1150] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_int.c.obj
[965/1150] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_lit.c.obj
[966/1150] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_rem.c.obj
[967/1150] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_rex.c.obj
[968/1150] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_str.c.obj
[969/1150] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/arg_utils.c.obj
[970/1150] Building C object esp-idf/console/CMakeFiles/__idf_console.dir/argtable3/argtable3.c.obj
[971/1150] Building C object esp-idf/protobuf-c/CMakeFiles/__idf_protobuf-c.dir/protobuf-c/protobuf-c/protobuf-c.c.obj
[972/1150] Building CXX object esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/Partition.cpp.obj
[973/1150] Building CXX object esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/SPI_Flash.cpp.obj
[974/1150] Building CXX object esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/WL_Ext_Perf.cpp.obj
[975/1150] Building CXX object esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/WL_Ext_Safe.cpp.obj
[976/1150] Linking C static library esp-idf\xtensa\libxtensa.a
[977/1150] Building CXX object esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/WL_Flash.cpp.obj
[978/1150] Building CXX object esp-idf/wear_levelling/CMakeFiles/__idf_wear_levelling.dir/wear_levelling.cpp.obj
[979/1150] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/unity/src/unity.c.obj
[980/1150] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_compat.c.obj
[981/1150] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_runner.c.obj
[982/1150] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_utils_freertos.c.obj
[983/1150] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_utils_cache.c.obj
[984/1150] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_utils_memory.c.obj
[985/1150] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/unity_port_esp32.c.obj
[986/1150] Building C object esp-idf/unity/CMakeFiles/__idf_unity.dir/port/esp/unity_utils_memory_esp.c.obj
[987/1150] Building C object esp-idf/esp_https_server/CMakeFiles/__idf_esp_https_server.dir/src/https_server.c.obj
[988/1150] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/common/protocomm.c.obj
[989/1150] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/proto-c/constants.pb-c.c.obj
[990/1150] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/proto-c/sec0.pb-c.c.obj
[991/1150] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/proto-c/sec1.pb-c.c.obj
[992/1150] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/proto-c/sec2.pb-c.c.obj
[993/1150] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/proto-c/session.pb-c.c.obj
[994/1150] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/transports/protocomm_console.c.obj
[995/1150] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/transports/protocomm_httpd.c.obj
[996/1150] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/security/security0.c.obj
[997/1150] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/security/security1.c.obj
[998/1150] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/security/security2.c.obj
[999/1150] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/crypto/srp6a/esp_srp.c.obj
[1000/1150] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/crypto/srp6a/esp_srp_mpi.c.obj
[1001/1150] Building C object esp-idf/protocomm/CMakeFiles/__idf_protocomm.dir/src/transports/protocomm_nimble.c.obj
[1002/1150] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/diskio/diskio.c.obj
[1003/1150] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/diskio/diskio_rawflash.c.obj
[1004/1150] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/diskio/diskio_wl.c.obj
[1005/1150] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/src/ff.c.obj
[1006/1150] Linking C static library esp-idf\protobuf-c\libprotobuf-c.a
[1007/1150] Linking C static library esp-idf\console\libconsole.a
[1008/1150] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/src/ffunicode.c.obj
[1009/1150] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/port/freertos/ffsystem.c.obj
[1010/1150] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/diskio/diskio_sdmmc.c.obj
[1011/1150] Building C object esp-idf/json/CMakeFiles/__idf_json.dir/cJSON/cJSON.c.obj
[1012/1150] Building C object esp-idf/json/CMakeFiles/__idf_json.dir/cJSON/cJSON_Utils.c.obj
[1013/1150] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/vfs/vfs_fat.c.obj
[1014/1150] Building C object esp-idf/joltwallet__littlefs/CMakeFiles/__idf_joltwallet__littlefs.dir/src/littlefs/lfs.c.obj
[1015/1150] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/vfs/vfs_fat_sdmmc.c.obj
[1016/1150] Building C object esp-idf/joltwallet__littlefs/CMakeFiles/__idf_joltwallet__littlefs.dir/src/littlefs/lfs_util.c.obj
[1017/1150] Building C object esp-idf/fatfs/CMakeFiles/__idf_fatfs.dir/vfs/vfs_fat_spiflash.c.obj
[1018/1150] Building C object esp-idf/joltwallet__littlefs/CMakeFiles/__idf_joltwallet__littlefs.dir/src/esp_littlefs.c.obj
[1019/1150] Building C object esp-idf/joltwallet__littlefs/CMakeFiles/__idf_joltwallet__littlefs.dir/src/littlefs_esp_part.c.obj
[1020/1150] Building C object esp-idf/joltwallet__littlefs/CMakeFiles/__idf_joltwallet__littlefs.dir/src/lfs_config.c.obj
[1021/1150] Building C object esp-idf/nimble_peripheral_utils/CMakeFiles/__idf_nimble_peripheral_utils.dir/misc.c.obj
[1022/1150] Building C object esp-idf/nimble_peripheral_utils/CMakeFiles/__idf_nimble_peripheral_utils.dir/scli.c.obj
[1023/1150] Building C object esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/app_trace.c.obj
[1024/1150] Building C object esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/app_trace_util.c.obj
[1025/1150] Building C object esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/host_file_io.c.obj
[1026/1150] Building C object esp-idf/app_trace/CMakeFiles/__idf_app_trace.dir/port/port_uart.c.obj
[1027/1150] Building C object esp-idf/cmock/CMakeFiles/__idf_cmock.dir/CMock/src/cmock.c.obj
[1028/1150] Building C object esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/esp_cam_ctlr.c.obj
[1029/1150] Linking C static library esp-idf\bt\libbt.a
[1030/1150] Linking C static library esp-idf\wear_levelling\libwear_levelling.a
[1031/1150] Building C object esp-idf/esp_driver_cam/CMakeFiles/__idf_esp_driver_cam.dir/dvp_share_ctrl.c.obj
[1032/1150] Building C object esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/src/esp_eth.c.obj
[1033/1150] Building C object esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/src/phy/esp_eth_phy_802_3.c.obj
[1034/1150] Building C object esp-idf/esp_eth/CMakeFiles/__idf_esp_eth.dir/src/esp_eth_netif_glue.c.obj
[1035/1150] Building C object esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/src/esp_hidd.c.obj
[1036/1150] Building C object esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/src/esp_hidh.c.obj
[1037/1150] Building C object esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/src/esp_hid_common.c.obj
[1038/1150] Building C object esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/src/nimble_hidd.c.obj
[1039/1150] Building C object esp-idf/esp_hid/CMakeFiles/__idf_esp_hid.dir/src/nimble_hidh.c.obj
[1040/1150] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_common.c.obj
[1041/1150] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_io.c.obj
[1042/1150] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_nt35510.c.obj
[1043/1150] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_ssd1306.c.obj
[1044/1150] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_st7789.c.obj
[1045/1150] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/src/esp_lcd_panel_ops.c.obj
[1046/1150] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/i2c/esp_lcd_panel_io_i2c_v1.c.obj
[1047/1150] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/i2c/esp_lcd_panel_io_i2c_v2.c.obj
[1048/1150] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/spi/esp_lcd_panel_io_spi.c.obj
[1049/1150] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/i80/esp_lcd_panel_io_i80.c.obj
[1050/1150] Building C object esp-idf/esp_lcd/CMakeFiles/__idf_esp_lcd.dir/rgb/esp_lcd_panel_rgb.c.obj
[1051/1150] Building C object esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/src/esp_local_ctrl.c.obj
[1052/1150] Linking C static library esp-idf\unity\libunity.a
[1053/1150] Building C object esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/src/esp_local_ctrl_handler.c.obj
[1054/1150] Building C object esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/proto-c/esp_local_ctrl.pb-c.c.obj
[1055/1150] Building C object esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/src/esp_local_ctrl_transport_ble.c.obj
[1056/1150] Building C object esp-idf/esp_local_ctrl/CMakeFiles/__idf_esp_local_ctrl.dir/src/esp_local_ctrl_transport_httpd.c.obj
[1057/1150] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_init.c.obj
[1058/1150] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_common.c.obj
[1059/1150] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_flash.c.obj
[1060/1150] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_uart.c.obj
[1061/1150] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_elf.c.obj
[1062/1150] Linking C static library esp-idf\esp_https_server\libesp_https_server.a
[1063/1150] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_binary.c.obj
[1064/1150] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_sha.c.obj
[1065/1150] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/core_dump_crc.c.obj
[1066/1150] Building C object esp-idf/espcoredump/CMakeFiles/__idf_espcoredump.dir/src/port/xtensa/core_dump_port.c.obj
[1067/1150] Building C object esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/esp-mqtt/mqtt_client.c.obj
[1068/1150] Building C object esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/esp-mqtt/lib/mqtt_msg.c.obj
[1069/1150] Building C object esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/esp-mqtt/lib/mqtt_outbox.c.obj
[1070/1150] Building C object esp-idf/mqtt/CMakeFiles/__idf_mqtt.dir/esp-mqtt/lib/platform_esp32_idf.c.obj
[1071/1150] Building C object esp-idf/nvs_sec_provider/CMakeFiles/__idf_nvs_sec_provider.dir/nvs_sec_provider.c.obj
[1072/1150] Building C object esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/xtensa_perfmon_access.c.obj
[1073/1150] Building C object esp-idf/perfmon/CMakeFiles/__idf_perfmon.dir/xtensa_perfmon_apis.c.obj
[1074/1150] Building C object esp-idf/rt/CMakeFiles/__idf_rt.dir/FreeRTOS_POSIX_mqueue.c.obj
[1075/1150] Building C object esp-idf/rt/CMakeFiles/__idf_rt.dir/FreeRTOS_POSIX_utils.c.obj
[1076/1150] Building C object esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs_api.c.obj
[1077/1150] Linking C static library esp-idf\protocomm\libprotocomm.a
[1078/1150] Building C object esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs/src/spiffs_cache.c.obj
[1079/1150] Building C object esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs/src/spiffs_check.c.obj
[1080/1150] Building C object esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs/src/spiffs_gc.c.obj
[1081/1150] Building C object esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs/src/spiffs_hydrogen.c.obj
[1082/1150] Building C object esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/spiffs/src/spiffs_nucleus.c.obj
[1083/1150] Building C object esp-idf/spiffs/CMakeFiles/__idf_spiffs.dir/esp_spiffs.c.obj
[1084/1150] Building C object esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/touch_element.c.obj
[1085/1150] Building C object esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/touch_button.c.obj
[1086/1150] Building C object esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/touch_slider.c.obj
[1087/1150] Building C object esp-idf/touch_element/CMakeFiles/__idf_touch_element.dir/touch_matrix.c.obj
[1088/1150] Linking C static library esp-idf\fatfs\libfatfs.a
[1089/1150] Building C object esp-idf/usb/CMakeFiles/__idf_usb.dir/hcd_dwc.c.obj
[1090/1150] Building C object esp-idf/usb/CMakeFiles/__idf_usb.dir/enum.c.obj
[1091/1150] Building C object esp-idf/usb/CMakeFiles/__idf_usb.dir/hub.c.obj
[1092/1150] Building C object esp-idf/usb/CMakeFiles/__idf_usb.dir/usb_helpers.c.obj
[1093/1150] Building C object esp-idf/usb/CMakeFiles/__idf_usb.dir/usb_host.c.obj
[1094/1150] Linking C static library esp-idf\json\libjson.a
[1095/1150] Building C object esp-idf/usb/CMakeFiles/__idf_usb.dir/usb_private.c.obj
[1096/1150] Building C object esp-idf/usb/CMakeFiles/__idf_usb.dir/usbh.c.obj
[1097/1150] Building C object esp-idf/usb/CMakeFiles/__idf_usb.dir/usb_phy.c.obj
[1098/1150] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/wifi_config.c.obj
[1099/1150] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/wifi_scan.c.obj
[1100/1150] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/wifi_ctrl.c.obj
[1101/1150] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/manager.c.obj
[1102/1150] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/handlers.c.obj
[1103/1150] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/scheme_console.c.obj
[1104/1150] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/proto-c/wifi_config.pb-c.c.obj
[1105/1150] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/proto-c/wifi_scan.pb-c.c.obj
[1106/1150] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/proto-c/wifi_ctrl.pb-c.c.obj
[1107/1150] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/proto-c/wifi_constants.pb-c.c.obj
[1108/1150] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/scheme_softap.c.obj
[1109/1150] Building C object esp-idf/wifi_provisioning/CMakeFiles/__idf_wifi_provisioning.dir/src/scheme_ble.c.obj
[1110/1150] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/ble_ota_service.c.obj
[1111/1150] Linking C static library esp-idf\joltwallet__littlefs\libjoltwallet__littlefs.a
[1112/1150] Linking C static library esp-idf\nimble_peripheral_utils\libnimble_peripheral_utils.a
[1113/1150] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/crc.c.obj
[1114/1150] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/ble_request_handler.c.obj
[1115/1150] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/fw_file_handler.c.obj
[1116/1150] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/flash_handler.c.obj
[1117/1150] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/ble_response_handler.c.obj
[1118/1150] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/ota_manager.c.obj
[1119/1150] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/gatt_svr.c.obj
[1120/1150] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/node_version.c.obj
[1121/1150] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/led_control.c.obj
[1122/1150] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/ota_handler.c.obj
[1123/1150] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/timer_handler.c.obj
D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main/Src/timer_handler.c:17:17: warning: 'timer_counter' defined but not used [-Wunused-variable]
   17 | static uint32_t timer_counter = 0;
      |                 ^~~~~~~~~~~~~
[1124/1150] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/utility.c.obj
[1125/1150] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/ble_app.c.obj
[1126/1150] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/gatt_svr_handler.c.obj
FAILED: esp-idf/main/CMakeFiles/__idf_main.dir/Src/gatt_svr_handler.c.obj 
ccache C:\Users\<USER>\.espressif\tools\xtensa-esp-elf\esp-14.2.0_20241119\xtensa-esp-elf\bin\xtensa-esp32s3-elf-gcc.exe -DESP_PLATFORM -DIDF_VER=\"v5.4.1\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -D_POSIX_READER_WRITER_LOCKS -ID:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/config -ID:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main/Inc -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/config/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/config/include/freertos -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/config/xtensa/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/esp_additions/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/dma/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/ldo/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/debug_probe/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/port/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/heap/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/heap/tlsf -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32s3/register -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/soc -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/include/private -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa/deprecated_include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/include/apps -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/include/apps/sntp -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/freertos/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/esp32xx/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/esp32xx/include/arch -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/esp32xx/include/sys -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/deprecated -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/i2c/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/touch_sensor/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/twai/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/touch_sensor/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_pm/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_ringbuf/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gpio/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_pcnt/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gptimer/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_spi/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ana_cmpr/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2s/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdmmc/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/sdmmc/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdspi/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdio/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_dac/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_rmt/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_tsens/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdm/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2c/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_uart/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/vfs/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ledc/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_parlio/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_usb_serial_jtag/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_partition/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_update/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_app_format/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_bootloader_format/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs/diskio -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs/src -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs/vfs -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/wear_levelling/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/include/esp32c3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/common/osi/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/common/api/include/api -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/common/btc/profile/esp/blufi/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/common/btc/profile/esp/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/common/hci_log/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/common/ble_log/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/ans/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/bas/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/dis/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/gap/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/gatt/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/hr/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/htp/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/ias/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/ipss/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/lls/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/prox/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/cts/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/tps/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/hid/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/sps/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/cte/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/util/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/store/ram/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/store/config/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/porting/nimble/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/port/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/transport/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/porting/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/porting/npl/freertos/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/esp-hci/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_timer/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/include/local -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/wifi_apps/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/wifi_apps/nan_app/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_event/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_phy/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_phy/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif/include -ID:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/managed_components/joltwallet__littlefs/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/examples/bluetooth/nimble/common/nimble_peripheral_utils -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -mdisable-hardware-atomics -Og -fno-shrink-wrap -fmacro-prefix-map=D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.4.1/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -std=gnu17 -Wno-old-style-declaration -MD -MT esp-idf/main/CMakeFiles/__idf_main.dir/Src/gatt_svr_handler.c.obj -MF esp-idf\main\CMakeFiles\__idf_main.dir\Src\gatt_svr_handler.c.obj.d -o esp-idf/main/CMakeFiles/__idf_main.dir/Src/gatt_svr_handler.c.obj -c D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main/Src/gatt_svr_handler.c
D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main/Src/gatt_svr_handler.c:40:10: fatal error: mbedtls/pk.h: No such file or directory
   40 | #include "mbedtls/pk.h"
      |          ^~~~~~~~~~~~~~
compilation terminated.

[1127/1150] Linking C static library esp-idf\app_trace\libapp_trace.a
[1128/1150] Linking C static library esp-idf\cmock\libcmock.a
[1129/1150] Linking C static library esp-idf\esp_driver_cam\libesp_driver_cam.a
[1130/1150] Linking C static library esp-idf\esp_eth\libesp_eth.a
[1131/1150] Linking C static library esp-idf\esp_lcd\libesp_lcd.a
[1132/1150] Linking C static library esp-idf\esp_hid\libesp_hid.a
[1133/1150] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/main.c.obj
[1134/1150] Linking C static library esp-idf\esp_local_ctrl\libesp_local_ctrl.a
[1135/1150] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/uart_handler.c.obj
D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main/Src/uart_handler.c: In function 'uart_read_state_machine':
D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main/Src/uart_handler.c:166:18: warning: unused variable 'offset' [-Wunused-variable]
  166 |         uint32_t offset = 0;
      |                  ^~~~~~
ninja: build stopped: subcommand failed.
