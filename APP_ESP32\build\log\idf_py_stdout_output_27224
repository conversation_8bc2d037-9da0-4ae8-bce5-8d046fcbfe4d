-- Found Git: C:/Users/<USER>/.espressif/tools/idf-git/2.39.2/cmd/git.exe (found version "2.39.2.windows.1")
-- ccache will be used for faster recompilation
-- The C compiler identification is GNU 14.2.0
-- The CXX compiler identification is GNU 14.2.0
-- The ASM compiler identification is GNU
-- Found assembler: C:/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc.exe
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: C:/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: C:/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Building ESP-IDF components for target esp32s3
NOTICE: Using component placed at C:\Users\<USER>\esp\v5.4.1\esp-idf\examples\bluetooth\nimble\common\nimble_peripheral_utils for dependency "nimble_peripheral_utils", specified in D:\BLE\Bitbucket\Feature_ble_fw_esp32\APP_ESP32\main\idf_component.yml
NOTICE: Processing 3 dependencies:
NOTICE: [1/3] joltwallet/littlefs (1.14.8)
NOTICE: [2/3] nimble_peripheral_utils (*) (C:\Users\<USER>\esp\v5.4.1\esp-idf\examples\bluetooth\nimble\common\nimble_peripheral_utils)
NOTICE: [3/3] idf (5.4.1)
-- Project sdkconfig file D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/sdkconfig
Loading defaults file D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/sdkconfig.defaults...
warning: unknown kconfig symbol 'BTDM_CTRL_MODE_BLE_ONLY' assigned to 'y' in D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/sdkconfig.defaults
warning: unknown kconfig symbol 'BTDM_CTRL_MODE_BR_EDR_ONLY' assigned to 'n' in D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/sdkconfig.defaults
warning: unknown kconfig symbol 'BTDM_CTRL_MODE_BTDM' assigned to 'n' in D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/sdkconfig.defaults
-- Compiler supported targets: xtensa-esp-elf
-- Found Python3: C:/Users/<USER>/.espressif/python_env/idf5.4_py3.11_env/Scripts/python.exe (found version "3.11.2") found components: Interpreter
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS - Success
-- App "Vantage_nxESP32_Int_Rel_1_2_0_1" version: Vantage_Bin_nxESP32_1_2_0_0
-- Adding linker script D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.bt_funcs.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- Components: app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_security esp_system esp_timer esp_vfs_console esp_wifi espcoredump esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 joltwallet__littlefs json log lwip main mbedtls mqtt newlib nimble_peripheral_utils nvs_flash nvs_sec_provider openthread partition_table perfmon protobuf-c protocomm pthread rt sdmmc soc spi_flash spiffs tcp_transport touch_element ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant xtensa
-- Component paths: C:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_trace C:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_update C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt C:/Users/<USER>/esp/v5.4.1/esp-idf/components/cmock C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console C:/Users/<USER>/esp/v5.4.1/esp-idf/components/cxx C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver C:/Users/<USER>/esp/v5.4.1/esp-idf/components/efuse C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp-tls C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_app_format C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_bootloader_format C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_coex C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_common C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ana_cmpr C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_cam C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_dac C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gpio C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gptimer C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2c C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2s C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_isp C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_jpeg C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ledc C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_parlio C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_pcnt C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ppa C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_rmt C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdio C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdm C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdmmc C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdspi C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_spi C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_touch_sens C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_tsens C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_uart C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_usb_serial_jtag C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_event C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_gdbstub C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hid C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_client C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_server C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_ota C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_server C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_local_ctrl C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_mm C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif_stack C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_partition C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_phy C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_pm C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_psram C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_ringbuf C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_security C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_timer C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_vfs_console C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi C:/Users/<USER>/esp/v5.4.1/esp-idf/components/espcoredump C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py C:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal C:/Users/<USER>/esp/v5.4.1/esp-idf/components/heap C:/Users/<USER>/esp/v5.4.1/esp-idf/components/http_parser C:/Users/<USER>/esp/v5.4.1/esp-idf/components/idf_test C:/Users/<USER>/esp/v5.4.1/esp-idf/components/ieee802154 D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/managed_components/joltwallet__littlefs C:/Users/<USER>/esp/v5.4.1/esp-idf/components/json C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mqtt C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib C:/Users/<USER>/esp/v5.4.1/esp-idf/examples/bluetooth/nimble/common/nimble_peripheral_utils C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_sec_provider C:/Users/<USER>/esp/v5.4.1/esp-idf/components/openthread C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table C:/Users/<USER>/esp/v5.4.1/esp-idf/components/perfmon C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protobuf-c C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm C:/Users/<USER>/esp/v5.4.1/esp-idf/components/pthread C:/Users/<USER>/esp/v5.4.1/esp-idf/components/rt C:/Users/<USER>/esp/v5.4.1/esp-idf/components/sdmmc C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spiffs C:/Users/<USER>/esp/v5.4.1/esp-idf/components/tcp_transport C:/Users/<USER>/esp/v5.4.1/esp-idf/components/touch_element C:/Users/<USER>/esp/v5.4.1/esp-idf/components/ulp C:/Users/<USER>/esp/v5.4.1/esp-idf/components/unity C:/Users/<USER>/esp/v5.4.1/esp-idf/components/usb C:/Users/<USER>/esp/v5.4.1/esp-idf/components/vfs C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wear_levelling C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant C:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa
-- Configuring done (28.0s)
-- Generating done (4.3s)
-- Build files have been written to: D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build
