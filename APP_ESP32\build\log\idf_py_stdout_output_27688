[0/1] Re-running CMake...
-- ccache will be used for faster recompilation
-- Building ESP-IDF components for target esp32s3
NOTICE: Using component placed at C:\Users\<USER>\esp\v5.4.1\esp-idf\examples\bluetooth\nimble\common\nimble_peripheral_utils for dependency "nimble_peripheral_utils", specified in D:\BLE\Bitbucket\Feature_ble_fw_esp32\APP_ESP32\main\idf_component.yml
NOTICE: Processing 3 dependencies:
NOTICE: [1/3] joltwallet/littlefs (1.14.8)
-- Configuring incomplete, errors occurred!
FAILED: build.ninja 
C:\Users\<USER>\.espressif\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SD:\BLE\Bitbucket\Feature_ble_fw_esp32\APP_ESP32 -BD:\BLE\Bitbucket\Feature_ble_fw_esp32\APP_ESP32\build
