[1/10] Performing build step for 'bootloader'
[1/1] C:\Windows\system32\cmd.exe /C "cd /D D:\BLE\Bitbucket\Feature_ble_fw_esp32\APP_ESP32\build\bootloader\esp-idf\esptool_py && C:\Users\<USER>\.espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/bootloader.bin"

Bootloader binary size 0x51e0 bytes. 0x2e20 bytes (36%) free.


[2/10] No install step for 'bootloader'
[3/10] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/main.c.obj
FAILED: esp-idf/main/CMakeFiles/__idf_main.dir/Src/main.c.obj 
ccache C:\Users\<USER>\.espressif\tools\xtensa-esp-elf\esp-14.2.0_20241119\xtensa-esp-elf\bin\xtensa-esp32s3-elf-gcc.exe -DESP_PLATFORM -DIDF_VER=\"v5.4.1\" -DMBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -DUNITY_INCLUDE_CONFIG_H -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -D_POSIX_READER_WRITER_LOCKS -ID:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/config -ID:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main/Inc -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/config/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/config/include/freertos -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/config/xtensa/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/esp_additions/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/dma/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/ldo/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/debug_probe/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/port/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/heap/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/heap/tlsf -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32s3/register -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/soc -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/include/private -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa/deprecated_include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/include/apps -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/include/apps/sntp -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/freertos/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/esp32xx/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/esp32xx/include/arch -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/esp32xx/include/sys -ID:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/managed_components/joltwallet__littlefs/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_partition/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/examples/bluetooth/nimble/common/nimble_peripheral_utils -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gpio/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_pm/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/port/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/mbedtls/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/mbedtls/library -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/esp_crt_bundle/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/mbedtls/3rdparty/everest/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/mbedtls/3rdparty/p256-m -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/mbedtls/3rdparty/p256-m/p256-m -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_app_format/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_bootloader_format/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_update/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_mm/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_security/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/pthread/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_timer/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gptimer/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_ringbuf/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_uart/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/vfs/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_trace/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_event/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_pcnt/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_spi/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ana_cmpr/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2s/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/sdmmc/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdmmc/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdspi/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdio/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_dac/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_rmt/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_tsens/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdm/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2c/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ledc/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_parlio/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_usb_serial_jtag/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/deprecated -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/i2c/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/touch_sensor/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/twai/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/touch_sensor/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_phy/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_phy/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_vfs_console/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/port/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_coex/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/include/local -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/wifi_apps/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/wifi_apps/nan_app/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/include/esp32c3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/common/osi/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/common/api/include/api -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/common/btc/profile/esp/blufi/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/common/btc/profile/esp/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/common/hci_log/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/common/ble_log/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/ans/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/bas/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/dis/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/gap/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/gatt/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/hr/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/htp/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/ias/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/ipss/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/lls/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/prox/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/cts/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/tps/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/hid/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/sps/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/cte/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/util/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/store/ram/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/store/config/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/porting/nimble/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/port/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/transport/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/porting/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/porting/npl/freertos/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/esp-hci/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/unity/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/unity/unity/src -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/cmock/CMock/src -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/console -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/http_parser -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp-tls -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp-tls/esp-tls-crypto -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc/interface -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc/deprecated/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_isp/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_cam/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_cam/interface -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_jpeg/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ppa/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_gdbstub/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hid/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/tcp_transport/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_client/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_server/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_ota/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_server/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_psram/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd/interface -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd/rgb/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/protobuf-c/protobuf-c -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm/include/common -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm/include/security -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm/include/transports -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm/include/crypto/srp6a -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm/proto-c -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_local_ctrl/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/espcoredump/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/espcoredump/include/port/xtensa -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/wear_levelling/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs/diskio -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs/src -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs/vfs -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/idf_test/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/idf_test/include/esp32s3 -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/ieee802154/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/json/cJSON -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/mqtt/esp-mqtt/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_sec_provider/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/perfmon/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/rt/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/spiffs/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/touch_element/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/usb/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning/include -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -mdisable-hardware-atomics -Og -fno-shrink-wrap -fmacro-prefix-map=D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.4.1/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -std=gnu17 -Wno-old-style-declaration -MD -MT esp-idf/main/CMakeFiles/__idf_main.dir/Src/main.c.obj -MF esp-idf\main\CMakeFiles\__idf_main.dir\Src\main.c.obj.d -o esp-idf/main/CMakeFiles/__idf_main.dir/Src/main.c.obj -c D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main/Src/main.c
D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main/Src/main.c: In function 'app_main':
D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main/Src/main.c:117:29: error: implicit declaration of function 'ota_manager_init' [-Wimplicit-function-declaration]
  117 |         esp_err_t ota_err = ota_manager_init();
      |                             ^~~~~~~~~~~~~~~~
[4/10] Completed 'bootloader'
ninja: build stopped: subcommand failed.
