/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : ble_ota_service.c
* @version        : 1.0.0
* @brief          : BLE OTA service implementation
* @details        : BLE OTA service for firmware updates via NimBLE
********************************************************************************/

#include "ble_ota_service.h"
#include "ota_manager.h"
#include "esp_log.h"
#include "host/ble_hs.h"
#include "host/ble_uuid.h"
#include "services/gap/ble_svc_gap.h"
#include "services/gatt/ble_svc_gatt.h"
#include <string.h>

static const char *TAG = "BLE_OTA_SERVICE";

/* Service and Characteristic UUIDs */
static const ble_uuid128_t ota_service_uuid = 
    BLE_UUID128_INIT(BLE_OTA_SERVICE_UUID);

static const ble_uuid128_t ota_data_char_uuid = 
    BLE_UUID128_INIT(BLE_OTA_DATA_CHAR_UUID);

static const ble_uuid128_t ota_control_char_uuid = 
    BLE_UUID128_INIT(BLE_OTA_CONTROL_CHAR_UUID);

static const ble_uuid128_t ota_status_char_uuid = 
    BLE_UUID128_INIT(BLE_OTA_STATUS_CHAR_UUID);

static const ble_uuid128_t ota_info_char_uuid = 
    BLE_UUID128_INIT(BLE_OTA_INFO_CHAR_UUID);

/* Global variables */
static uint16_t ota_status_handle;
static uint16_t ota_connection_handle = BLE_HS_CONN_HANDLE_NONE;
static ota_progress_callback_t progress_callback = NULL;
static ota_complete_callback_t complete_callback = NULL;

/* Forward declarations */
static int ota_data_access(uint16_t conn_handle, uint16_t attr_handle,
                          struct ble_gatt_access_ctxt *ctxt, void *arg);
static int ota_control_access(uint16_t conn_handle, uint16_t attr_handle,
                             struct ble_gatt_access_ctxt *ctxt, void *arg);
static int ota_status_access(uint16_t conn_handle, uint16_t attr_handle,
                            struct ble_gatt_access_ctxt *ctxt, void *arg);
static int ota_info_access(uint16_t conn_handle, uint16_t attr_handle,
                          struct ble_gatt_access_ctxt *ctxt, void *arg);

/* GATT Service Definition */
static const struct ble_gatt_svc_def ota_gatt_svcs[] = {
    {
        .type = BLE_GATT_SVC_TYPE_PRIMARY,
        .uuid = &ota_service_uuid.u,
        .characteristics = (struct ble_gatt_chr_def[]) {
            {
                /* OTA Data Characteristic */
                .uuid = &ota_data_char_uuid.u,
                .access_cb = ota_data_access,
                .flags = BLE_GATT_CHR_F_WRITE | BLE_GATT_CHR_F_WRITE_NO_RSP,
            },
            {
                /* OTA Control Characteristic */
                .uuid = &ota_control_char_uuid.u,
                .access_cb = ota_control_access,
                .flags = BLE_GATT_CHR_F_WRITE | BLE_GATT_CHR_F_READ,
            },
            {
                /* OTA Status Characteristic */
                .uuid = &ota_status_char_uuid.u,
                .access_cb = ota_status_access,
                .flags = BLE_GATT_CHR_F_READ | BLE_GATT_CHR_F_NOTIFY,
                .val_handle = &ota_status_handle,
            },
            {
                /* OTA Info Characteristic */
                .uuid = &ota_info_char_uuid.u,
                .access_cb = ota_info_access,
                .flags = BLE_GATT_CHR_F_WRITE | BLE_GATT_CHR_F_READ,
            },
            {
                0, /* No more characteristics */
            }
        }
    },
    {
        0, /* No more services */
    }
};

/**
 * @brief OTA Data Characteristic access callback
 */
static int ota_data_access(uint16_t conn_handle, uint16_t attr_handle,
                          struct ble_gatt_access_ctxt *ctxt, void *arg)
{
    ESP_LOGI(TAG, "!!! OTA Data access callback invoked, op=%d, handle=%d, len=%d !!!", ctxt->op, attr_handle, ctxt->om ? ctxt->om->om_len : 0);

    // Set connection handle for status notifications
    ota_connection_handle = conn_handle;
    
    switch (ctxt->op) {
    case BLE_GATT_ACCESS_OP_WRITE_CHR:
        if (ctxt->om->om_len < sizeof(ota_chunk_header_t)) {
            ESP_LOGE(TAG, "Invalid chunk size: %d", ctxt->om->om_len);
            return BLE_ATT_ERR_INVALID_ATTR_VALUE_LEN;
        }
        
        /* Extract header and firmware data */
        if (ctxt->om->om_len < sizeof(ota_chunk_header_t)) {
            ESP_LOGE(TAG, "Chunk too small for header: %d", ctxt->om->om_len);
            return BLE_ATT_ERR_INVALID_ATTR_VALUE_LEN;
        }

        // Skip the header and get only the firmware data
        uint8_t *firmware_data = ctxt->om->om_data + sizeof(ota_chunk_header_t);
        uint16_t firmware_data_len = ctxt->om->om_len - sizeof(ota_chunk_header_t);

        /* Extract sequence number from header for ACK */
        ota_chunk_header_t *header = (ota_chunk_header_t *)ctxt->om->om_data;
        uint32_t sequence_number = header->sequence_number;

        /* Process only the firmware data (without header) */
        esp_err_t err = ota_manager_process_chunk(firmware_data, firmware_data_len);
        if (err != ESP_OK) {
            ESP_LOGE(TAG, "Failed to process chunk: %s", esp_err_to_name(err));
            /* Send error status */
            ble_ota_send_status(OTA_STATUS_ERROR, OTA_ERROR_WRITE_FAILED, sequence_number);
            return BLE_ATT_ERR_UNLIKELY;
        }

        /* Send ACK for successful chunk processing */
        ble_ota_send_status(OTA_STATUS_ACK, OTA_ERROR_NONE, sequence_number);

        return 0;
        
    default:
        return BLE_ATT_ERR_UNLIKELY;
    }
}

/**
 * @brief OTA Control Characteristic access callback
 */
static int ota_control_access(uint16_t conn_handle, uint16_t attr_handle,
                             struct ble_gatt_access_ctxt *ctxt, void *arg)
{
    ESP_LOGI(TAG, "!!! OTA Control access callback invoked, op=%d, handle=%d, len=%d !!!", ctxt->op, attr_handle, ctxt->om ? ctxt->om->om_len : 0);

    // Set connection handle for status notifications
    ota_connection_handle = conn_handle;
    
    switch (ctxt->op) {
    case BLE_GATT_ACCESS_OP_WRITE_CHR:
        if (ctxt->om->om_len < 1) {
            return BLE_ATT_ERR_INVALID_ATTR_VALUE_LEN;
        }
        
        ota_command_t cmd = ctxt->om->om_data[0];
        ESP_LOGI(TAG, "Received OTA command: %d", cmd);

        esp_err_t err = ota_manager_handle_command(cmd);
        if (err != ESP_OK) {
            ESP_LOGE(TAG, "Failed to handle command: %s", esp_err_to_name(err));
            return BLE_ATT_ERR_UNLIKELY;
        }
        
        return 0;
        
    case BLE_GATT_ACCESS_OP_READ_CHR:
        /* Return current OTA status */
        ota_status_t status = ota_manager_get_status();
        return os_mbuf_append(ctxt->om, &status, sizeof(status));
        
    default:
        return BLE_ATT_ERR_UNLIKELY;
    }
}

/**
 * @brief OTA Status Characteristic access callback
 */
static int ota_status_access(uint16_t conn_handle, uint16_t attr_handle,
                            struct ble_gatt_access_ctxt *ctxt, void *arg)
{
    switch (ctxt->op) {
    case BLE_GATT_ACCESS_OP_READ_CHR:
        /* Return current OTA status response */
        ota_status_response_t response;
        ota_manager_get_status_response(&response);
        return os_mbuf_append(ctxt->om, &response, sizeof(response));

    default:
        return BLE_ATT_ERR_UNLIKELY;
    }
}

/**
 * @brief OTA Info Characteristic access callback
 */
static int ota_info_access(uint16_t conn_handle, uint16_t attr_handle,
                          struct ble_gatt_access_ctxt *ctxt, void *arg)
{
    ESP_LOGI(TAG, "!!! OTA Info access callback invoked, op=%d, handle=%d, len=%d !!!", ctxt->op, attr_handle, ctxt->om ? ctxt->om->om_len : 0);
    switch (ctxt->op) {
    case BLE_GATT_ACCESS_OP_WRITE_CHR:
        if (ctxt->om->om_len != sizeof(ota_firmware_info_t)) {
            ESP_LOGE(TAG, "Invalid firmware info size: %d", ctxt->om->om_len);
            return BLE_ATT_ERR_INVALID_ATTR_VALUE_LEN;
        }

        /* Set firmware info */
        esp_err_t err = ota_manager_set_firmware_info((ota_firmware_info_t*)ctxt->om->om_data);
        if (err != ESP_OK) {
            ESP_LOGE(TAG, "Failed to set firmware info: %s", esp_err_to_name(err));
            return BLE_ATT_ERR_UNLIKELY;
        }

        return 0;

    case BLE_GATT_ACCESS_OP_READ_CHR:
        /* Return current firmware info */
        ota_firmware_info_t info;
        esp_err_t read_err = ota_manager_get_firmware_info(&info);
        if (read_err != ESP_OK) {
            return BLE_ATT_ERR_UNLIKELY;
        }
        return os_mbuf_append(ctxt->om, &info, sizeof(info));

    default:
        return BLE_ATT_ERR_UNLIKELY;
    }
}

/**
 * @brief Initialize BLE OTA service
 */
esp_err_t ble_ota_service_init(void)
{
    ESP_LOGI(TAG, "Initializing BLE OTA service");

    int rc = ble_gatts_count_cfg(ota_gatt_svcs);
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to count GATT configuration: %d", rc);
        return ESP_FAIL;
    }

    rc = ble_gatts_add_svcs(ota_gatt_svcs);
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to add GATT services: %d", rc);
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "BLE OTA service initialized successfully");
    ESP_LOGI(TAG, "OTA Status handle: %d", ota_status_handle);
    return ESP_OK;
}

/**
 * @brief Deinitialize BLE OTA service
 */
esp_err_t ble_ota_service_deinit(void)
{
    ESP_LOGI(TAG, "Deinitializing BLE OTA service");
    ota_connection_handle = BLE_HS_CONN_HANDLE_NONE;
    return ESP_OK;
}

/**
 * @brief Send OTA status notification
 */
esp_err_t ble_ota_send_status(ota_status_t status, ota_error_t error, uint32_t sequence)
{
    if (ota_connection_handle == BLE_HS_CONN_HANDLE_NONE) {
        ESP_LOGW(TAG, "No active connection for status notification");
        return ESP_ERR_INVALID_STATE;
    }

    ota_status_response_t response = {
        .status = status,
        .error = error,
        .sequence_number = sequence,
        .bytes_received = ota_manager_get_bytes_received()
    };

    BLE_OTA_LOGV(TAG, "Sending notification: status=%d, error=%d, sequence=%lu, bytes=%lu",
             response.status, response.error, response.sequence_number, response.bytes_received);

    struct os_mbuf *om = ble_hs_mbuf_from_flat(&response, sizeof(response));
    if (om == NULL) {
        ESP_LOGE(TAG, "Failed to allocate mbuf for status notification");
        return ESP_ERR_NO_MEM;
    }

    int rc = ble_gattc_notify_custom(ota_connection_handle, ota_status_handle, om);
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to send status notification: %d", rc);
        return ESP_FAIL;
    }

    return ESP_OK;
}

/**
 * @brief Get current connection handle
 */
esp_err_t ble_ota_get_connection_handle(uint16_t *conn_handle)
{
    if (conn_handle == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    *conn_handle = ota_connection_handle;
    return ESP_OK;
}

/**
 * @brief Register progress callback
 */
esp_err_t ble_ota_register_progress_callback(ota_progress_callback_t callback)
{
    progress_callback = callback;
    return ESP_OK;
}

/**
 * @brief Register complete callback
 */
esp_err_t ble_ota_register_complete_callback(ota_complete_callback_t callback)
{
    complete_callback = callback;
    return ESP_OK;
}
