/******************************************************************************
 * @Copyright (C) 2025 by Vantage Elevation
 * @file           : led_control.c
 * @version        : 1.0.0
 * @brief          : LED Control and State Management for System Indication
 * @details        : Provides GPIO-based LED behavior for system status:
 *                   - RED LED for system errors
 *                   - AMBER LED for UART activity
 *                   - BLUE LED for BLE status
 *                   - GREEN LED for heartbeat
 *                   Each LED operates on a state machine using FreeRTOS tasks.
 *
 * @change-log     :
 *   07/23/2025, [1.0.0] : [*] Initial version with LED task creation and
 *                         behavior APIs for RED, BLUE, GREEN, and AMBER LEDs.
 ******************************************************************************/

/*******************************************************************************
 * Includes
 ******************************************************************************/
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/gpio.h"

/*******************************************************************************
 * Macros & Definitions
 ******************************************************************************/
// Define LED GPIOs
#define LED_RED                  10  // System error / health
#define LED_AMBER                11  // UART activity
#define LED_BLUE                 13  // BLE status
#define LED_GREEN                14  // Heartbeat      

#define LED_ON                    0   // LED ON state  
#define LED_OFF                   1   // LED OFF state

/*******************************************************************************
 * Delay Macros (Refactored from literals)
 ******************************************************************************/
#define LED_DELAY_NORMAL_MS      500   // Normal blink ON/OFF delay
#define LED_DELAY_FAST_MS        250   // Fast blink ON/OFF delay
#define LED_HEARTBEAT_ON_MS      100   // GREEN ON duration
#define LED_HEARTBEAT_OFF_MS     900   // GREEN OFF duration
#define LED_UART_PULSE_MS        10    // AMBER pulse duration
#define LED_FALLBACK_DELAY_MS    100   // Default fallback delay

/*******************************************************************************
 * Static Variables
 ******************************************************************************/
// Define LED behavior states
typedef enum 
{
    LED_STATE_OFF,
    LED_STATE_ON,
    LED_STATE_BLINK_FAST,
    LED_STATE_BLINK_SLOW,
    LED_STATE_BLINK_NORMAL
} led_state_t;

// Current LED states for control in FreeRTOS tasks
static led_state_t red_state = LED_STATE_OFF;
static led_state_t blue_state = LED_STATE_OFF;

static TaskHandle_t amber_task_handle = NULL;

/*******************************************************************************
 * LED Initialization
 ******************************************************************************/

/**
 * @brief Initialize a single GPIO pin for LED output.
 *
 * Configures the given GPIO pin as an output and sets the initial
 * level to OFF (logic HIGH, active-low LED). This is used internally
 * by `led_init_all()` for all LED pins.
 *
 * @param pin GPIO number
 */
static void led_init_pin(gpio_num_t pin) 
{
    gpio_reset_pin(pin);                        // Reset GPIO configuration
    gpio_set_direction(pin, GPIO_MODE_OUTPUT);  // Set as output
    gpio_set_level(pin, 1);                     // Turn LED OFF initially
}

/**
 * @brief Initialize all system LEDs.
 *
 * Configures the RED, AMBER, BLUE, and GREEN GPIOs for output.
 * Ensures all LEDs are set to OFF initially.
 *
 * @note Must be called before starting LED tasks.
 */
void led_init_all() 
{
    led_init_pin(LED_RED);     // Error/health indicator
    led_init_pin(LED_AMBER);   // UART activity
    led_init_pin(LED_BLUE);    // BLE status
    led_init_pin(LED_GREEN);   // Heartbeat
}


/*******************************************************************************
 * Common LED Method Implementations
 ******************************************************************************/
/**
 * @brief Generic LED blink helper to reduce repetitive ON/OFF code.
 *
 * @param pin GPIO number of the LED
 * @param on_time_ms ON duration in milliseconds
 * @param off_time_ms OFF duration in milliseconds
 */
void led_blink(gpio_num_t pin, int on_time_ms, int off_time_ms)
{
    gpio_set_level(pin, LED_ON);
    vTaskDelay(pdMS_TO_TICKS(on_time_ms));
    gpio_set_level(pin, LED_OFF);
    vTaskDelay(pdMS_TO_TICKS(off_time_ms));
}

/*******************************************************************************
 * LED Task Implementations
 ******************************************************************************/

/**
 * @brief Task to manage RED LED behavior.
 *
 * Controls RED LED states based on system error or communication failure:
 * - OFF: No error
 * - ON: Communication failure (solid RED)
 * - BLINK_NORMAL: Generic system error
 *
 * @param arg
 */
static void red_led_task(void *arg) 
{
    while (1) {
        switch (red_state) {
            case LED_STATE_OFF: 
                gpio_set_level(LED_RED, LED_OFF);             // Turn OFF
                vTaskDelay(pdMS_TO_TICKS(500));               // Idle wait
                break;

            case LED_STATE_ON:  
                gpio_set_level(LED_RED, LED_ON);              // Solid ON
                vTaskDelay(pdMS_TO_TICKS(500));               // Periodic refresh
                break;

            case LED_STATE_BLINK_NORMAL:
                // Blink Red LED 500ms
                led_blink(LED_RED, LED_DELAY_NORMAL_MS, LED_DELAY_NORMAL_MS);
                break;

            default:
                vTaskDelay(pdMS_TO_TICKS(100));               // Fallback delay
                break;
        }
    }
}

/**
 * @brief Task to manage BLUE LED behavior (BLE state).
 *
 * Displays BLE status using various blink patterns:
 * - OFF: Neither advertising nor Connected.
 * - ON: Connected
 * - BLINK_FAST(500 ms): Advertising
 * - BLINK_SLOW(1 sec): BLE is talking, and is in Transitional state
 *
 * @param arg
 */
static void blue_led_task(void *arg) 
{
    while (1) {
        switch (blue_state) {
            case LED_STATE_OFF: 
                gpio_set_level(LED_BLUE, LED_OFF);            // OFF
                vTaskDelay(pdMS_TO_TICKS(500));               // Idle delay
                break;

            case LED_STATE_ON:  
                gpio_set_level(LED_BLUE, LED_ON);             // Solid ON
                vTaskDelay(pdMS_TO_TICKS(500));               // Refresh period
                break;

            case LED_STATE_BLINK_FAST:
                // Blink Blue Led ON:250ms and OFF:250ms 
                led_blink(LED_BLUE, LED_DELAY_FAST_MS, LED_DELAY_FAST_MS);
                break;

            case LED_STATE_BLINK_SLOW:
                // ON:500ms->OFF:500ms->ON
                led_blink(LED_BLUE, LED_DELAY_NORMAL_MS, LED_DELAY_NORMAL_MS);
                gpio_set_level(LED_BLUE, LED_ON);             // ON
                blue_state = LED_STATE_ON;
                break;

            default:
                vTaskDelay(pdMS_TO_TICKS(100));               // Invalid state fallback
                break;
        }
    }
}

/**
 * @brief Task to control GREEN LED as heartbeat.
 *
 * Provides a heartbeat indication using a 1-second blink cycle
 * (100ms ON, 900ms OFF).
 *
 * @param arg
 */
static void green_led_task(void *arg) 
{
    while (1) {
        // Pulse ON: 100ms -> Pulse OFF: 900ms (1 sec cycle) 
        led_blink(LED_GREEN, LED_HEARTBEAT_ON_MS, LED_HEARTBEAT_OFF_MS);
    }
}

/*******************************************************************************
 * AMBER LED: UART Activity Indicator
 ******************************************************************************/

/**
 * @brief Trigger a short AMBER LED pulse for UART activity.
 *
 * Sends a task notification to the AMBER LED task to indicate UART
 * transmission or reception. LED lights briefly to signal activity.
 *
 * @note Called from UART handler code.
 */
void led_uart_activity() 
{
    // Send notification to amber LED task
    if (amber_task_handle != NULL) {
        xTaskNotifyGive(amber_task_handle);
    }
}

/**
 * @brief Task to pulse AMBER LED on UART activity.
 *
 * Waits for task notifications via "led_uart_activity()", then turns
 * AMBER LED ON for 10ms to visually signal UART communication.
 *
 * @param arg
 */
static void amber_led_task(void *arg) 
{
    while (1) {
        // Wait for UART activity notification
        ulTaskNotifyTake(pdTRUE, portMAX_DELAY);  // Block until notified

        gpio_set_level(LED_AMBER, LED_ON);             // Turn ON
        vTaskDelay(pdMS_TO_TICKS(LED_UART_PULSE_MS));  // ON for 10ms
        gpio_set_level(LED_AMBER, LED_OFF);            // Turn OFF
    }
}

/*******************************************************************************
 * LED State API
 ******************************************************************************/

/**
 * @brief Set RED LED state based on error and comm status.
 *
 * @param has_error True if system has any error.
 * @param comm_loss True if there's communication failure.
 *
 * LED behavior:-
 * -------------------------------------------------
 * | has_error | comm_loss  | LED State            |
 * |-----------|------------|----------------------|
 * | false     | false      | OFF                  |
 * | true      | false      | BLINK (generic error)|
 * | true      | true       | ON (comm loss)       |
 * -------------------------------------------------
 */
void led_set_error(bool has_error, bool comm_loss) 
{
    if (!has_error) {
        red_state = LED_STATE_OFF;                            // No error, LED OFF
    }
    else if (comm_loss) {
        red_state = LED_STATE_ON;  
    }                                                         // Comm lost, LED ON
    else {
        red_state = LED_STATE_BLINK_NORMAL;                   // Generic error, blink
    }
}

/**
 * @brief Set BLUE LED behavior according to BLE connection state.
 *
 * Allows the application to reflect BLE activity by setting LED state:
 * - ON: Connected
 * - BLINK_FAST: Advertising
 * - OFF: Disconnected
 *
 * @param state Desired LED behavior (from "led_state_t" enum).
 */
void led_set_ble_status(led_state_t state) 
{
    blue_state = state;                                       // Set BLE indicator state
}

/**
 * @brief Start all LED-related FreeRTOS tasks.
 *
 * Creates tasks for each LED: RED (error), BLUE (BLE), GREEN (heartbeat),
 * and AMBER (UART activity). Each task manages the behavior of its
 * respective LED based on shared state variables.
 *
 * @note Must be called after GPIO initialization ("led_init_all()").
 */
void led_start_tasks()
{
    xTaskCreate(red_led_task, "red_led", 1024, NULL, 0, NULL);      // RED LED task (error)
    xTaskCreate(blue_led_task, "blue_led", 1024, NULL, 0, NULL);    // BLUE LED task (BLE)
    xTaskCreate(green_led_task, "green_led", 1024, NULL, 0, NULL);  // GREEN LED task (heartbeat)
    xTaskCreate(amber_led_task, "amber_led", 1024, NULL, 0, &amber_task_handle);  // AMBER LED (UART Activity)
}


