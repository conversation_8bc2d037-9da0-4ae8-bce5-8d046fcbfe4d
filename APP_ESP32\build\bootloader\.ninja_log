# ninja log v6
25	1034	7755705026395671	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_timestamp.c.obj	f83e44f21f901685
68	1240	7755705026719912	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj	c2e49dae8332393a
118	1463	7755705027200530	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_lock.c.obj	b5e87001bc69fec4
178	1579	7755705027880233	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	c682aee1daebbb5e
240	1724	7755705028537747	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj	dd6239af6335d820
312	1845	7755705029275462	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	75983807d0c6550a
433	1921	7755705030490850	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	a7f3483c8f075d0a
691	2019	7755705033072571	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	887ad8966bf87f20
894	2144	7755705035102112	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	9869316161f1e42a
1041	2293	7755705036575480	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj	e5d10a1737688e3
1241	2434	7755705038545746	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	7d185c20b1fdc6cf
1725	2517	7755705043410670	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj	ba83749ffc209caf
534	2669	7755705031500298	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	2bc42ed5747bc341
1463	2815	7755705040783812	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj	60a559932a62cb97
1580	2977	7755705041959199	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj	5e5d6cab9b7f4366
1922	3104	7755705045362456	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	a899fb33ddbd2df4
2144	3316	7755705047580470	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj	e73e0cbf31510e2c
2020	3405	7755705046366436	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	eedb715fc52d1998
2294	3521	7755705049100558	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	bf8515f08e6b30b1
2434	3697	7755705050504538	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj	61ebb76299390e73
1845	3809	7755705044611641	esp-idf/log/liblog.a	e54b20c7fa2964d7
2670	4071	7755705052864904	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj	fd28e15f7791601
2518	4192	7755705051348075	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj	5b443093f80d2fcd
2816	4488	7755705054320845	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj	48ea26fbb7353a17
2978	4607	7755705055939475	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj	c0bf79a33a3a966c
3316	4680	7755705059320025	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj	a3d1c232ec156460
3104	4804	7755705057202862	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj	4bfe0e7c38df55ad
3405	4955	7755705060216305	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	bffb6e4346a6f891
3521	5104	7755705061356848	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj	b17df1134d02787
3697	5294	7755705063136472	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj	1298ec82f28650dd
4072	5575	7755705066882115	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj	b43ecd807a7fe6e1
4193	5674	7755705068092790	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj	984f45d1bdd858ed
4488	5890	7755705071034648	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	2e673cb66c8c557
4608	6050	7755705072242726	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	4965e824ce0d69f6
3809	6252	7755705064252353	esp-idf/esp_rom/libesp_rom.a	805d24816b3a60c9
4681	6300	7755705072949435	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	ae97c3779b25d814
4805	6430	7755705074201351	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	61f45e6509e1486a
4956	6693	7755705075725137	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	143c0cb5c4772aaa
5575	6898	7755705081903075	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	2361422639f63810
5295	6991	7755705079046588	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	e1a9586b680c3c5f
5105	7074	7755705077215033	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	eaa1d9f30df50085
5674	7288	7755705082884405	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	a463245cca6390ec
5890	7450	7755705085062531	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	ae6c203d51eef8b1
6050	7584	7755705086664405	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	71b9743d9ed5da87
6301	7776	7755705089172503	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	a30498979c3f4366
6430	7953	7755705090443681	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj	9df60cefedf39ea3
6898	8333	7755705095143748	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	6f918bcff2a8774d
6253	8535	7755705088687810	esp-idf/esp_common/libesp_common.a	b71236a9bcb917c9
6694	8729	7755705093099319	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	2a6da2106c2a3108
6991	8862	7755705096060292	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj	7ad56293ddb9a5d6
7288	8968	7755705099044672	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	a32df1f5c33d9256
7777	9133	7755705103915401	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	5c7c7ccbdeb9e007
7074	9393	7755705096912417	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	1a9c882ae89fc2b4
7584	9528	7755705101986280	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	8d4a4faddd03c48d
7450	9769	7755705100664360	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	e87f461648b0b1f6
7953	9884	7755705105696437	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	2b591bacdc03593f
8333	10036	7755705109492034	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	62b3cb3af83bf7c4
8729	10147	7755705113442123	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj	d94e33c62e2bcda0
8863	10345	7755705114794904	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj	479687e421f1e72d
9393	10573	7755705120079861	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	44d2ddf6416cfa03
9133	10747	7755705117486151	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	fc54b7091d361441
9528	10958	7755705121431573	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	da612cb17c158e8f
8968	11072	7755705115849169	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj	269406c78723102f
9769	11253	7755705123840045	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	58c9cc4c7ebc503a
8535	11414	7755705111514052	esp-idf/esp_hw_support/libesp_hw_support.a	b3e17a15a8dc9385
9885	11514	7755705125010541	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	eaf5bc897b3f041a
10037	11696	7755705126540671	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	d9590d63968d6166
10148	11831	7755705127638999	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj	19587ff2692ded53
10345	11974	7755705129597483	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	72e5bc03abc51642
10573	12098	7755705131888726	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	a88c4ecbca3c5fe5
11073	12260	7755705136889398	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	e17d90a7cd874f0e
11257	12390	7755705138735045	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj	e2764f1ff992994b
10959	12538	7755705135752844	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	75404385d42b11f4
11514	12681	7755705141302438	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj	999b773e11e29e28
11697	12820	7755705143129077	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj	35a22860f0b4bf73
11831	12937	7755705144475512	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj	7dd7c130e614c583
11974	13121	7755705145892736	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj	a0809e503ef67c7f
12099	13307	7755705147148396	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj	571566a91022656b
12260	13441	7755705148758943	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj	b09220aabc2aef7d
10747	13571	7755705133625039	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	347040af540d0376
12538	13643	7755705151540963	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj	314c349a4af25f75
12390	13729	7755705150061782	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj	faa4e2a4eb9342e0
11414	13822	7755705140292109	esp-idf/esp_system/libesp_system.a	24bb0c2fe6a0c356
12820	13866	7755705154392847	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj	76bb0d5d8f7c150a
12681	13935	7755705152978100	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj	a38a4ab26e1046f2
12938	14047	7755705155558329	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj	371b7ab80dd4abab
13122	14184	7755705157382807	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj	c789c90c18fa154d
13307	14284	7755705159236722	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj	5cbb9b9c4ca71c65
13572	14398	7755705161892206	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj	3452635f0fabe6a8
13441	14516	7755705160578762	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj	d5e7d1b185545051
13644	14591	7755705162598305	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj	d012e1b6cecb7fab
13729	14691	7755705163458726	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj	75d5ddb1643d2728
13935	14796	7755705165498889	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj	ed7148953d48bc5d
13866	14933	7755705164809680	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj	fd38888be186abab
14047	14935	7755705166631780	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj	19a739fcab5aa2f5
14184	14954	7755705167997345	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/wdt_periph.c.obj	40035d70a4af072f
14285	14962	7755705169007170	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj	b7f14fb0075c618f
14516	15179	7755705171314134	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj	ba52318d24e50e57
14399	15182	7755705170135128	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj	cd459680db3b1a8
14591	15315	7755705172070089	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj	1f7cd98b26682029
13822	15495	7755705164381878	esp-idf/efuse/libefuse.a	3c1c1105f6217f7f
14797	15541	7755705174129738	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	27344fe5b8035443
14692	15542	7755705177798057	project_elf_src_esp32s3.c	fe633a5f4d72cd2d
14692	15542	7755705177798057	D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/project_elf_src_esp32s3.c	fe633a5f4d72cd2d
15542	15836	7755705181570210	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj	503356affbd4e6fb
15495	16524	7755705181114502	esp-idf/bootloader_support/libbootloader_support.a	49c58ff06b01234d
16524	17234	7755705191406275	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	3a9c204458b8a058
17234	18174	7755705198490731	esp-idf/spi_flash/libspi_flash.a	af13735a3c836284
18174	19562	7755705207901600	esp-idf/hal/libhal.a	60eb6124c554fe19
19563	20985	7755705221784413	esp-idf/micro-ecc/libmicro-ecc.a	db7c16ad8cac7493
20986	22882	7755705236017572	esp-idf/soc/libsoc.a	fbb706948c61c9c9
22882	24188	7755705254980779	esp-idf/xtensa/libxtensa.a	18ee135e1db9edc2
24188	25661	7755705268044235	esp-idf/main/libmain.a	a1fa28df99b21283
25661	27166	7755705282770969	bootloader.elf	d4dbe372795bfcb8
27166	29661	7755705318626606	.bin_timestamp	6821153c18a480a8
27166	29661	7755705318626606	D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/.bin_timestamp	6821153c18a480a8
29662	31281	7755705322781185	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	e6feb8641ee77c4f
29662	31281	7755705322781185	D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	e6feb8641ee77c4f
