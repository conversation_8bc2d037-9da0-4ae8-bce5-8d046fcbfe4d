# ninja log v6
145	1514	7755542545913421	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_lock.c.obj	b5e87001bc69fec4
29	1712	7755542544748089	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_timestamp.c.obj	f83e44f21f901685
87	1822	7755542545336904	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj	c2e49dae8332393a
212	1988	7755542546588675	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	c682aee1daebbb5e
533	2416	7755542549795105	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	a7f3483c8f075d0a
390	2561	7755542548368105	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	75983807d0c6550a
275	3104	7755542547217186	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj	dd6239af6335d820
777	3191	7755542552235907	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	887ad8966bf87f20
645	3292	7755542550921597	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	2bc42ed5747bc341
884	3396	7755542553309041	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	9869316161f1e42a
1519	3480	7755542559647498	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj	e5d10a1737688e3
1713	3556	7755542561585656	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	7d185c20b1fdc6cf
1822	3682	7755542562692038	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj	60a559932a62cb97
2416	3809	7755542568630076	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj	ba83749ffc209caf
1989	3887	7755542564357969	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj	5e5d6cab9b7f4366
3105	4891	7755542575521281	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	a899fb33ddbd2df4
3191	5030	7755542576374574	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	eedb715fc52d1998
3292	5190	7755542577379657	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj	e73e0cbf31510e2c
3397	5295	7755542578434798	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	bf8515f08e6b30b1
3480	5441	7755542579259639	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj	61ebb76299390e73
3556	5756	7755542580035144	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj	5b443093f80d2fcd
2562	5924	7755542570083275	esp-idf/log/liblog.a	e54b20c7fa2964d7
3683	6005	7755542581296054	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj	fd28e15f7791601
3887	6133	7755542583338299	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj	c0bf79a33a3a966c
3810	6356	7755542582555707	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj	48ea26fbb7353a17
5030	7050	7755542594772568	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj	a3d1c232ec156460
4892	7180	7755542593387996	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj	4bfe0e7c38df55ad
5191	7270	7755542596379471	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	bffb6e4346a6f891
5296	7381	7755542597425883	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj	b17df1134d02787
5441	7540	7755542598882479	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj	1298ec82f28650dd
5757	7664	7755542602042869	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj	b43ecd807a7fe6e1
6005	7941	7755542604512618	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj	984f45d1bdd858ed
6133	8133	7755542605796653	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	2e673cb66c8c557
6357	8375	7755542608034513	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	4965e824ce0d69f6
7050	9275	7755542614950528	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	ae97c3779b25d814
5924	9411	7755542603704156	esp-idf/esp_rom/libesp_rom.a	805d24816b3a60c9
7181	9514	7755542616283643	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	61f45e6509e1486a
7541	9624	7755542619882835	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	e1a9586b680c3c5f
7270	9722	7755542617174325	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	143c0cb5c4772aaa
7664	9836	7755542621114176	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	2361422639f63810
7942	9939	7755542623890018	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	a463245cca6390ec
7381	10066	7755542618264015	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	eaa1d9f30df50085
8134	10181	7755542625802519	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	ae6c203d51eef8b1
8375	10278	7755542628176456	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	71b9743d9ed5da87
9275	11249	7755542637226036	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	a30498979c3f4366
9515	11412	7755542639601453	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj	9df60cefedf39ea3
10066	11967	7755542645130503	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	a32df1f5c33d9256
9723	12182	7755542641692150	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	6f918bcff2a8774d
9624	12267	7755542640706272	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	2a6da2106c2a3108
9836	12406	7755542642804884	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj	7ad56293ddb9a5d6
9412	12511	7755542638583513	esp-idf/esp_common/libesp_common.a	b71236a9bcb917c9
9939	12803	7755542643868534	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	1a9c882ae89fc2b4
11249	12929	7755542656954198	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	5c7c7ccbdeb9e007
10278	13046	7755542647238634	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	8d4a4faddd03c48d
10181	13309	7755542646206098	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	e87f461648b0b1f6
11413	13516	7755542658594686	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	2b591bacdc03593f
12183	13887	7755542666301409	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj	d94e33c62e2bcda0
11967	14088	7755542664130436	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	62b3cb3af83bf7c4
12268	14180	7755542667137731	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj	479687e421f1e72d
12929	14676	7755542673756414	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	44d2ddf6416cfa03
12803	14798	7755542672501970	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	fc54b7091d361441
12406	14973	7755542668535348	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj	269406c78723102f
13047	15228	7755542674922784	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	da612cb17c158e8f
13310	15470	7755542677557328	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	58c9cc4c7ebc503a
13516	15644	7755542679639122	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	eaf5bc897b3f041a
13887	16156	7755542683312284	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	d9590d63968d6166
12511	16347	7755542669579096	esp-idf/esp_hw_support/libesp_hw_support.a	b3e17a15a8dc9385
14089	16558	7755542685358832	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj	19587ff2692ded53
14180	16661	7755542686261150	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	72e5bc03abc51642
14973	16817	7755542694194579	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	75404385d42b11f4
14677	16914	7755542691229894	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	a88c4ecbca3c5fe5
15228	17038	7755542696727138	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	e17d90a7cd874f0e
15645	17202	7755542700916526	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj	999b773e11e29e28
15471	17324	7755542699180226	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj	e2764f1ff992994b
14798	17528	7755542692430662	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	347040af540d0376
16157	17914	7755542706034390	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj	35a22860f0b4bf73
16558	18183	7755542710046232	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj	7dd7c130e614c583
16817	18431	7755542712636218	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj	571566a91022656b
16661	18603	7755542711071753	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj	a0809e503ef67c7f
16914	18804	7755542713611522	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj	b09220aabc2aef7d
17202	19009	7755542716489482	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj	314c349a4af25f75
17529	19159	7755542719750369	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj	76bb0d5d8f7c150a
17038	19274	7755542714850458	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj	faa4e2a4eb9342e0
17324	19376	7755542717710634	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj	a38a4ab26e1046f2
16347	19771	7755542707936051	esp-idf/esp_system/libesp_system.a	24bb0c2fe6a0c356
17915	19917	7755542723603499	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj	371b7ab80dd4abab
18183	20045	7755542726298894	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj	c789c90c18fa154d
18603	20372	7755542730480253	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj	d5e7d1b185545051
18431	20511	7755542728775246	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj	5cbb9b9c4ca71c65
18804	20649	7755542732497296	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj	3452635f0fabe6a8
19010	20774	7755542734570327	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj	d012e1b6cecb7fab
19274	21043	7755542737202025	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj	fd38888be186abab
19376	21166	7755542738194866	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj	ed7148953d48bc5d
19160	21269	7755542736070256	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj	75d5ddb1643d2728
19917	21510	7755542743640528	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj	19a739fcab5aa2f5
20046	21749	7755542744934721	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/wdt_periph.c.obj	40035d70a4af072f
20372	21876	7755542748194703	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj	b7f14fb0075c618f
20650	22126	7755542750959132	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj	ba52318d24e50e57
20511	22135	7755542749540094	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj	cd459680db3b1a8
21044	22311	7755542762918979	project_elf_src_esp32s3.c	fe633a5f4d72cd2d
21044	22311	7755542762918979	D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/project_elf_src_esp32s3.c	fe633a5f4d72cd2d
20774	22395	7755542752213760	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj	1f7cd98b26682029
21166	22509	7755542756135074	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	27344fe5b8035443
19771	22604	7755542742173146	esp-idf/efuse/libefuse.a	3c1c1105f6217f7f
22312	22927	7755542767578904	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj	503356affbd4e6fb
22604	23911	7755542770480414	esp-idf/bootloader_support/libbootloader_support.a	49c58ff06b01234d
23911	24796	7755542783567864	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	3a9c204458b8a058
24796	25728	7755542792375428	esp-idf/spi_flash/libspi_flash.a	af13735a3c836284
25728	27836	7755542801749628	esp-idf/hal/libhal.a	60eb6124c554fe19
27836	29963	7755542822828549	esp-idf/micro-ecc/libmicro-ecc.a	db7c16ad8cac7493
29963	33195	7755542844095195	esp-idf/soc/libsoc.a	fbb706948c61c9c9
33195	35737	7755542876421316	esp-idf/xtensa/libxtensa.a	18ee135e1db9edc2
35737	38277	7755542901844041	esp-idf/main/libmain.a	a1fa28df99b21283
38277	40743	7755542927236326	bootloader.elf	d4dbe372795bfcb8
40743	45889	7755542997876648	.bin_timestamp	6821153c18a480a8
40743	45889	7755542997876648	D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/.bin_timestamp	6821153c18a480a8
45890	48203	7755543003361204	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	e6feb8641ee77c4f
45890	48203	7755543003361204	D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	e6feb8641ee77c4f
