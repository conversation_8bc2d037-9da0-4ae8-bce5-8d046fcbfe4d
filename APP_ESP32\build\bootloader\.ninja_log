# ninja log v6
28	1303	7755594491195881	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_timestamp.c.obj	f83e44f21f901685
100	1532	7755594491934133	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj	c2e49dae8332393a
458	1797	7755594495514300	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	75983807d0c6550a
303	1972	7755594493925123	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	c682aee1daebbb5e
200	2093	7755594492904284	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_lock.c.obj	b5e87001bc69fec4
382	2242	7755594494650957	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj	dd6239af6335d820
573	2348	7755594496644344	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	a7f3483c8f075d0a
753	2805	7755594498464623	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	2bc42ed5747bc341
968	3074	7755594500609145	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	887ad8966bf87f20
1308	3182	7755594504018783	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj	e5d10a1737688e3
1145	3328	7755594502384650	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	9869316161f1e42a
1533	3513	7755594506258877	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	7d185c20b1fdc6cf
2094	3627	7755594511844474	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj	ba83749ffc209caf
1797	3792	7755594508911937	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj	60a559932a62cb97
1973	4057	7755594510646452	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj	5e5d6cab9b7f4366
2349	4316	7755594514414386	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	a899fb33ddbd2df4
3328	4716	7755594524209108	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj	61ebb76299390e73
3182	4971	7755594522752244	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	bf8515f08e6b30b1
3075	5213	7755594521680808	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj	e73e0cbf31510e2c
2806	5491	7755594518991951	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	eedb715fc52d1998
3628	5740	7755594527212605	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj	fd28e15f7791601
2243	6001	7755594513358575	esp-idf/log/liblog.a	e54b20c7fa2964d7
3513	6165	7755594526067297	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj	5b443093f80d2fcd
4057	6346	7755594531475373	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj	c0bf79a33a3a966c
4317	6554	7755594534097408	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj	4bfe0e7c38df55ad
3793	6796	7755594528833158	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj	48ea26fbb7353a17
4716	7149	7755594538097685	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj	a3d1c232ec156460
4972	7306	7755594540640268	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	bffb6e4346a6f891
5491	7571	7755594545843030	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj	1298ec82f28650dd
5213	7798	7755594543061000	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj	b17df1134d02787
5741	8085	7755594548331997	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj	b43ecd807a7fe6e1
6166	8324	7755594552595535	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj	984f45d1bdd858ed
6555	8575	7755594556478325	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	4965e824ce0d69f6
6346	8876	7755594554399168	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	2e673cb66c8c557
6797	9246	7755594558894845	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	ae97c3779b25d814
6001	9423	7755594550949007	esp-idf/esp_rom/libesp_rom.a	805d24816b3a60c9
7149	9571	7755594562406755	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	61f45e6509e1486a
7307	9813	7755594563998746	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	143c0cb5c4772aaa
7799	9964	7755594568898901	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	e1a9586b680c3c5f
8086	10117	7755594571775666	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	2361422639f63810
7580	10268	7755594566736005	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	eaa1d9f30df50085
8325	10540	7755594574138409	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	a463245cca6390ec
8575	10747	7755594576672676	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	ae6c203d51eef8b1
8876	10989	7755594579694835	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	71b9743d9ed5da87
9247	11187	7755594583402186	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	a30498979c3f4366
9572	11475	7755594586630209	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj	9df60cefedf39ea3
10541	11998	7755594596339739	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	a32df1f5c33d9256
9964	12201	7755594590576231	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	6f918bcff2a8774d
9814	12357	7755594589070728	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	2a6da2106c2a3108
10117	12654	7755594592101663	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj	7ad56293ddb9a5d6
9424	12859	7755594585171116	esp-idf/esp_common/libesp_common.a	b71236a9bcb917c9
11187	13023	7755594602757516	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	5c7c7ccbdeb9e007
10268	13236	7755594593622141	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	1a9c882ae89fc2b4
10989	13349	7755594600823432	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	8d4a4faddd03c48d
11476	13502	7755594605698551	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	2b591bacdc03593f
10748	13730	7755594598498192	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	e87f461648b0b1f6
12201	13997	7755594612942954	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj	d94e33c62e2bcda0
12357	14263	7755594614510249	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj	479687e421f1e72d
11998	14489	7755594610913999	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	62b3cb3af83bf7c4
13024	14802	7755594621173241	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	fc54b7091d361441
13237	15067	7755594623307801	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	44d2ddf6416cfa03
13502	15264	7755594625950532	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	58c9cc4c7ebc503a
12654	15445	7755594617475971	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj	269406c78723102f
13350	15581	7755594624430318	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	da612cb17c158e8f
13730	15869	7755594628236874	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	eaf5bc897b3f041a
13997	16086	7755594630891310	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	d9590d63968d6166
14264	16521	7755594633567344	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj	19587ff2692ded53
14489	16775	7755594635825430	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	72e5bc03abc51642
14802	16950	7755594638956056	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	a88c4ecbca3c5fe5
15445	17086	7755594645384400	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	e17d90a7cd874f0e
12859	17171	7755594619522335	esp-idf/esp_hw_support/libesp_hw_support.a	b3e17a15a8dc9385
15264	17272	7755594643580269	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	75404385d42b11f4
15581	17417	7755594646744508	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj	e2764f1ff992994b
15870	17546	7755594649625610	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj	999b773e11e29e28
16086	17733	7755594651797037	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj	35a22860f0b4bf73
15068	17884	7755594641617806	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	347040af540d0376
16522	18044	7755594656153781	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj	7dd7c130e614c583
16776	18294	7755594658691001	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj	a0809e503ef67c7f
16950	18439	7755594660442127	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj	571566a91022656b
17086	18692	7755594661794039	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj	b09220aabc2aef7d
17273	18854	7755594663649745	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj	faa4e2a4eb9342e0
17418	19015	7755594665110933	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj	314c349a4af25f75
17734	19148	7755594668278669	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj	76bb0d5d8f7c150a
17546	19457	7755594666409733	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj	a38a4ab26e1046f2
17884	19705	7755594669772733	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj	371b7ab80dd4abab
19016	20753	7755594681091541	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj	75d5ddb1643d2728
19149	21452	7755594682424797	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj	fd38888be186abab
18044	22375	7755594671376266	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj	c789c90c18fa154d
18295	23254	7755594673889040	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj	5cbb9b9c4ca71c65
19457	24229	7755594685501768	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj	ed7148953d48bc5d
18692	24997	7755594677857479	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj	3452635f0fabe6a8
18440	25745	7755594675334489	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj	d5e7d1b185545051
17171	26742	7755594662674865	esp-idf/esp_system/libesp_system.a	24bb0c2fe6a0c356
18855	27634	7755594679483648	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj	d012e1b6cecb7fab
20597	28380	7755594696908155	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj	19a739fcab5aa2f5
21356	29269	7755594704489716	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/wdt_periph.c.obj	40035d70a4af072f
22234	30159	7755594713273582	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj	b7f14fb0075c618f
23170	30801	7755594722633909	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj	cd459680db3b1a8
24046	31580	7755594731403048	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj	ba52318d24e50e57
24901	32213	7755594739941801	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj	1f7cd98b26682029
25617	32976	7755594752020231	project_elf_src_esp32s3.c	fe633a5f4d72cd2d
25617	32976	7755594752020231	D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/project_elf_src_esp32s3.c	fe633a5f4d72cd2d
26588	33660	7755594756810635	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	27344fe5b8035443
27569	34285	7755594766626222	esp-idf/efuse/libefuse.a	3c1c1105f6217f7f
33581	35152	7755594826744713	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj	503356affbd4e6fb
35030	38147	7755594841231450	esp-idf/bootloader_support/libbootloader_support.a	49c58ff06b01234d
38147	40346	7755594872374322	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	3a9c204458b8a058
40346	42338	7755594894395527	esp-idf/spi_flash/libspi_flash.a	af13735a3c836284
42338	44289	7755594914312689	esp-idf/hal/libhal.a	60eb6124c554fe19
44289	46151	7755594933818552	esp-idf/micro-ecc/libmicro-ecc.a	db7c16ad8cac7493
46151	48676	7755594952445530	esp-idf/soc/libsoc.a	fbb706948c61c9c9
48676	50261	7755594977693494	esp-idf/xtensa/libxtensa.a	18ee135e1db9edc2
50261	52168	7755594993538804	esp-idf/main/libmain.a	a1fa28df99b21283
52168	54689	7755595012614726	bootloader.elf	d4dbe372795bfcb8
54689	58280	7755595071131438	.bin_timestamp	6821153c18a480a8
54689	58280	7755595071131438	D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/.bin_timestamp	6821153c18a480a8
58281	58943	7755595073727587	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	e6feb8641ee77c4f
58281	58943	7755595073727587	D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	e6feb8641ee77c4f
