#!/usr/bin/env python3
"""
Test BLE Security and Bonding with ESP32
Tests the enhanced security implementation
"""

import asyncio
import sys
from nimble_ota_client import NimBLEOTAClient

async def test_ble_security():
    """Test BLE security and bonding functionality"""
    print("🔐 Testing BLE Security and Bonding")
    print("=" * 50)
    
    # Create client (will auto-discover ESP32 devices)
    client = NimBLEOTAClient(device_address=None, verbose=True)
    
    try:
        # Test connection with security
        print("📡 Connecting to ESP32...")
        if not await client.connect():
            print("❌ Connection failed")
            return False
        
        print("✅ Connection successful!")
        
        # Test service discovery
        print("🔍 Testing service discovery...")
        services = client.client.services
        print(f"Found {len(services)} services:")
        for service in services:
            print(f"  - {service.uuid}")
        
        # Test OTA service access
        print("🔧 Testing OTA service access...")
        ota_service_uuid = "def09abc-5678-1234-def0-9abc56781234"
        ota_service = None
        
        for service in services:
            if str(service.uuid).lower() == ota_service_uuid.lower():
                ota_service = service
                break
        
        if ota_service:
            print("✅ OTA service found!")
            print("📋 Characteristics:")
            for char in ota_service.characteristics:
                print(f"  - {char.uuid} (properties: {char.properties})")
        else:
            print("❌ OTA service not found")
        
        # Test characteristic access (this will trigger security if needed)
        print("🔐 Testing characteristic access...")
        try:
            # Try to read a characteristic to test security
            if ota_service:
                for char in ota_service.characteristics:
                    if "read" in [prop.lower() for prop in char.properties]:
                        try:
                            data = await client.client.read_gatt_char(char.uuid)
                            print(f"✅ Successfully read characteristic {char.uuid}: {len(data)} bytes")
                            break
                        except Exception as e:
                            print(f"⚠️ Could not read characteristic {char.uuid}: {e}")
        except Exception as e:
            print(f"❌ Characteristic access failed: {e}")
        
        print("✅ Security test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Security test failed: {e}")
        return False
        
    finally:
        # Cleanup
        try:
            await client.disconnect()
        except:
            pass

async def main():
    """Main test function"""
    print("🧪 BLE Security Test Suite")
    print("=" * 50)
    
    success = await test_ble_security()
    
    if success:
        print("\n🎉 All security tests passed!")
        print("Your ESP32 BLE security is working correctly.")
    else:
        print("\n❌ Security tests failed!")
        print("Check ESP32 configuration and try again.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
